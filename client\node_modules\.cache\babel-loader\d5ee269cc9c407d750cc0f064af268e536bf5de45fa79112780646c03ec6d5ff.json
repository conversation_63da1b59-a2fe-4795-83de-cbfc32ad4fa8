{"ast": null, "code": "import _objectSpread from\"D:/\\u8206\\u60C5\\u76D1\\u63A7/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Row,Col,Card,Statistic,List,Typography,Tag,Space,Spin,Alert}from'antd';import{FileTextOutlined,DatabaseOutlined,ClockCircleOutlined,TrophyOutlined,RiseOutlined,PieChartOutlined,Bar<PERSON>hartOutlined,LineChartOutlined}from'@ant-design/icons';import{Column,Pie,Line,Area}from'@ant-design/charts';import ReactECharts from'echarts-for-react';import*as echarts from'echarts';import'echarts-wordcloud';import axios from'axios';import moment from'moment';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;const Dashboard=()=>{var _stats$byCategory,_stats$bySource,_trendingStats$trendi,_stats$general,_stats$bySource2,_stats$byCategory2,_stats$general2;const[loading,setLoading]=useState(true);const[stats,setStats]=useState(null);const[recentNews,setRecentNews]=useState([]);const[trendingStats,setTrendingStats]=useState(null);const[timelineStats,setTimelineStats]=useState([]);const[sentimentStats,setSentimentStats]=useState(null);const[error,setError]=useState(null);useEffect(()=>{fetchDashboardData();},[]);const fetchDashboardData=async()=>{try{setLoading(true);setError(null);const[statsRes,newsRes,trendingRes,timelineRes,sentimentRes]=await Promise.all([axios.get('/api/stats'),axios.get('/api/news?limit=10'),axios.get('/api/stats/trending?period=day&limit=5'),axios.get('/api/stats/timeline?period=day&days=7'),axios.get('/api/stats/sentiment?period=day&days=7&limit=50')]);setStats(statsRes.data.data);setRecentNews(newsRes.data.data.items);setTrendingStats(trendingRes.data.data);setTimelineStats(timelineRes.data.data.timeline);setSentimentStats(sentimentRes.data.data);}catch(error){console.error('获取仪表板数据失败:',error);setError('获取数据失败，请稍后重试');}finally{setLoading(false);}};if(loading){return/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'50px'},children:[/*#__PURE__*/_jsx(Spin,{size:\"large\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:\"\\u52A0\\u8F7D\\u4E2D...\"})]});}if(error){return/*#__PURE__*/_jsx(Alert,{message:\"\\u9519\\u8BEF\",description:error,type:\"error\",showIcon:true,action:/*#__PURE__*/_jsx(\"button\",{onClick:fetchDashboardData,children:\"\\u91CD\\u8BD5\"})});}const getCategoryColor=category=>{const colors={'技术社区':'blue','问答社区':'green','社交媒体':'orange','新闻媒体':'red','财经媒体':'purple','科技媒体':'cyan','视频平台':'magenta','短视频':'lime','投资社区':'gold'};return colors[category]||'default';};// 准备分类饼图数据\nconst categoryPieData=(stats===null||stats===void 0?void 0:(_stats$byCategory=stats.byCategory)===null||_stats$byCategory===void 0?void 0:_stats$byCategory.map(item=>({type:item._id,value:item.count,name:item._id})))||[];// 准备数据源柱状图数据\nconst sourceColumnData=(stats===null||stats===void 0?void 0:(_stats$bySource=stats.bySource)===null||_stats$bySource===void 0?void 0:_stats$bySource.slice(0,10).map(item=>({source:item._id.sourceName,count:item.count})))||[];// 准备词云数据\nconst wordCloudData=(trendingStats===null||trendingStats===void 0?void 0:(_trendingStats$trendi=trendingStats.trendingKeywords)===null||_trendingStats$trendi===void 0?void 0:_trendingStats$trendi.map(item=>({name:item._id,value:item.count})))||[];// 调试输出\nconsole.log('词云数据:',wordCloudData);console.log('热门统计数据:',trendingStats);// 准备时间线图表数据\nconst timelineChartData=timelineStats.map(item=>{const{_id}=item;let timeLabel;if(_id.hour!==undefined){timeLabel=\"\".concat(_id.month.toString().padStart(2,'0'),\"-\").concat(_id.day.toString().padStart(2,'0'),\" \").concat(_id.hour,\":00\");}else if(_id.day!==undefined){timeLabel=\"\".concat(_id.month.toString().padStart(2,'0'),\"-\").concat(_id.day.toString().padStart(2,'0'));}else if(_id.week!==undefined){timeLabel=\"\\u7B2C\".concat(_id.week,\"\\u5468\");}else{timeLabel=\"\".concat(_id.year,\"-\").concat(_id.month.toString().padStart(2,'0'));}return{time:timeLabel,count:item.count};});// 词云配置\nconst wordCloudOption={backgroundColor:'transparent',tooltip:{show:true,formatter:function(params){return\"\".concat(params.name,\": \").concat(params.value,\" \\u6B21\");}},series:[{type:'wordCloud',// 高级布局优化参数\ngridSize:12,// 进一步增加网格大小，确保词语不重叠\nsizeRange:[18,45],// 调整字体大小范围，减小最大字体避免过度拥挤\nrotationRange:[0,0],// 保持水平排列，避免旋转造成的混乱\nrotationStep:0,shape:'circle',// 使用圆形布局，更加紧凑\nleft:'center',top:'center',width:'85%',// 进一步缩小显示区域，增加边距\nheight:'85%',right:null,bottom:null,drawOutOfBound:false,layoutAnimation:true,// 布局算法优化\nmaskImage:null,keepAspect:true,// 保持宽高比，避免变形\n// 词语间距优化\npadding:8,// 增加词语间距\n// 布局尝试次数，提高布局质量\nlayoutAnimationDuration:1000,layoutAnimationEasing:'cubicOut',textStyle:{fontFamily:'Microsoft YaHei, PingFang SC, Helvetica Neue, Arial, sans-serif',fontWeight:'600',// 使用半粗体，平衡清晰度和美观\ncolor:function(params){// 根据词频大小使用渐变色系\nconst value=params.value;const maxValue=Math.max(...(wordCloudData||[]).map(item=>item.value));const ratio=value/maxValue;// 使用蓝色系渐变，高频词颜色更深\nif(ratio>0.8){return'#1890ff';// 深蓝\n}else if(ratio>0.6){return'#40a9ff';// 中蓝\n}else if(ratio>0.4){return'#69c0ff';// 浅蓝\n}else if(ratio>0.2){return'#91d5ff';// 很浅蓝\n}else{return'#bae7ff';// 极浅蓝\n}}},emphasis:{focus:'self',textStyle:{shadowBlur:6,shadowColor:'rgba(24, 144, 255, 0.4)',shadowOffsetX:1,shadowOffsetY:1,fontSize:function(params){// hover时字体稍微放大\nreturn params.fontSize*1.1;}}},data:wordCloudData&&wordCloudData.length>0?wordCloudData:[{name:'暂无数据',value:1}]}]};// 分类饼图配置\nconst categoryPieConfig={appendPadding:10,data:categoryPieData,angleField:'value',colorField:'type',radius:0.75,innerRadius:0.3,label:{type:'inner',offset:'-30%',content:function(data){return data.value>20?\"\".concat(data.type,\"\\n\").concat(data.value):'';},style:{fontSize:11,textAlign:'center',fill:'white',fontWeight:'bold'}},legend:{position:'right',offsetX:-20,itemName:{style:{fontSize:12}}},tooltip:{formatter:datum=>{return{name:datum.type,value:\"\".concat(datum.value,\" \\u6761\")};}},statistic:{title:{style:{whiteSpace:'pre-wrap',overflow:'hidden',textOverflow:'ellipsis'},content:'总计'},content:{style:{whiteSpace:'pre-wrap',overflow:'hidden',textOverflow:'ellipsis'},content:categoryPieData.reduce((sum,item)=>sum+item.value,0).toString()}},interactions:[{type:'element-selected'},{type:'element-active'}]};// 数据源柱状图配置\nconst sourceColumnConfig={data:sourceColumnData,xField:'source',yField:'count',label:{position:'middle',style:{fill:'#FFFFFF',opacity:0.6}},xAxis:{label:{autoHide:true,autoRotate:false}},meta:{source:{alias:'数据源'},count:{alias:'新闻数量'}}};// 时间线图表配置\nconst timelineConfig={data:timelineChartData,xField:'time',yField:'count',smooth:true,padding:[20,20,50,60],// 增加左侧padding确保Y轴显示\npoint:{size:4,shape:'circle',style:{fill:'white',stroke:'#1890ff',lineWidth:2}},line:{style:{stroke:'#1890ff',lineWidth:3}},area:{style:{fill:'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff',fillOpacity:0.3}},xAxis:{position:'bottom',label:{autoRotate:true,style:{fontSize:12}},title:{text:'时间',position:'end',style:{fontSize:14,fontWeight:'bold'}},grid:{line:{style:{stroke:'#f0f0f0',lineWidth:1,lineDash:[4,5]}}}},yAxis:{position:'left',min:0,nice:true,label:{style:{fontSize:12}},title:{text:'更新数量',position:'end',style:{fontSize:14,fontWeight:'bold'}},grid:{line:{style:{stroke:'#f0f0f0',lineWidth:1,lineDash:[4,5]}}}},tooltip:{showMarkers:true,formatter:datum=>{return{name:'更新数量',value:\"\".concat(datum.count,\" \\u6761\")};}},animation:{appear:{animation:'path-in',duration:1000}}};return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:2,children:\"\\u4EEA\\u8868\\u677F\"}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u603B\\u65B0\\u95FB\\u6570\\u91CF\",value:(stats===null||stats===void 0?void 0:(_stats$general=stats.general)===null||_stats$general===void 0?void 0:_stats$general.totalCount)||0,prefix:/*#__PURE__*/_jsx(FileTextOutlined,{}),valueStyle:{color:'#3f8600'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6570\\u636E\\u6E90\\u6570\\u91CF\",value:(stats===null||stats===void 0?void 0:(_stats$bySource2=stats.bySource)===null||_stats$bySource2===void 0?void 0:_stats$bySource2.length)||0,prefix:/*#__PURE__*/_jsx(DatabaseOutlined,{}),valueStyle:{color:'#1890ff'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5206\\u7C7B\\u6570\\u91CF\",value:(stats===null||stats===void 0?void 0:(_stats$byCategory2=stats.byCategory)===null||_stats$byCategory2===void 0?void 0:_stats$byCategory2.length)||0,prefix:/*#__PURE__*/_jsx(TrophyOutlined,{}),valueStyle:{color:'#722ed1'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6700\\u65B0\\u66F4\\u65B0\",value:stats!==null&&stats!==void 0&&(_stats$general2=stats.general)!==null&&_stats$general2!==void 0&&_stats$general2.latestCollectTime?moment(stats.general.latestCollectTime).fromNow():'暂无数据',prefix:/*#__PURE__*/_jsx(ClockCircleOutlined,{}),valueStyle:{color:'#fa8c16'}})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginBottom:24},children:[/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u5206\\u7C7B\\u5206\\u5E03\",extra:/*#__PURE__*/_jsx(PieChartOutlined,{}),children:categoryPieData.length>0?/*#__PURE__*/_jsx(Pie,_objectSpread(_objectSpread({},categoryPieConfig),{},{height:300})):/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'50px'},children:[/*#__PURE__*/_jsx(Spin,{}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:\"\\u52A0\\u8F7D\\u4E2D...\"})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u70ED\\u95E8\\u5173\\u952E\\u8BCD\\u8BCD\\u4E91\",extra:/*#__PURE__*/_jsx(RiseOutlined,{}),children:trendingStats&&trendingStats.trendingKeywords?/*#__PURE__*/_jsx(ReactECharts,{option:wordCloudOption,style:{height:'300px'},opts:{renderer:'canvas'},notMerge:true,lazyUpdate:true}):/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'50px'},children:[/*#__PURE__*/_jsx(Spin,{}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:\"\\u52A0\\u8F7D\\u4E2D...\"})]})})})]}),/*#__PURE__*/_jsx(Row,{gutter:[16,16],style:{marginBottom:24},children:/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsx(Card,{title:\"\\u6570\\u636E\\u66F4\\u65B0\\u8D8B\\u52BF\",extra:/*#__PURE__*/_jsx(LineChartOutlined,{}),children:timelineChartData.length>0?/*#__PURE__*/_jsx(Area,_objectSpread(_objectSpread({},timelineConfig),{},{height:300})):/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'50px'},children:[/*#__PURE__*/_jsx(Spin,{}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:\"\\u52A0\\u8F7D\\u4E2D...\"})]})})})}),/*#__PURE__*/_jsx(Row,{gutter:[16,16],style:{marginBottom:24},children:/*#__PURE__*/_jsx(Col,{span:24,children:/*#__PURE__*/_jsx(Card,{title:\"\\u70ED\\u95E8\\u6570\\u636E\\u6E90\\u7EDF\\u8BA1\",extra:/*#__PURE__*/_jsx(BarChartOutlined,{}),children:sourceColumnData.length>0?/*#__PURE__*/_jsx(Column,_objectSpread(_objectSpread({},sourceColumnConfig),{},{height:300})):/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'50px'},children:[/*#__PURE__*/_jsx(Spin,{}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:\"\\u52A0\\u8F7D\\u4E2D...\"})]})})})}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u6700\\u65B0\\u65B0\\u95FB\",extra:/*#__PURE__*/_jsx(\"a\",{href:\"/news\",children:\"\\u67E5\\u770B\\u66F4\\u591A\"}),style:{height:'400px'},bodyStyle:{height:'340px',overflow:'auto'},children:/*#__PURE__*/_jsx(List,{dataSource:recentNews,renderItem:item=>/*#__PURE__*/_jsx(List.Item,{style:{padding:'8px 0'},children:/*#__PURE__*/_jsx(List.Item.Meta,{title:/*#__PURE__*/_jsx(\"a\",{href:\"/news/\".concat(item._id),style:{fontSize:14},children:item.title.length>50?\"\".concat(item.title.substring(0,50),\"...\"):item.title}),description:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Tag,{color:getCategoryColor(item.category),size:\"small\",children:item.category}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:item.sourceName}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",style:{fontSize:12},children:moment(item.collectTime).fromNow()})]})})})})})}),/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u70ED\\u95E8\\u5206\\u7C7B\",extra:/*#__PURE__*/_jsx(RiseOutlined,{}),style:{height:'400px'},bodyStyle:{height:'340px',overflow:'auto'},children:/*#__PURE__*/_jsx(List,{dataSource:(trendingStats===null||trendingStats===void 0?void 0:trendingStats.trendingCategories)||[],renderItem:item=>/*#__PURE__*/_jsxs(List.Item,{style:{padding:'8px 0'},children:[/*#__PURE__*/_jsx(List.Item.Meta,{title:item._id,description:\"\".concat(item.count,\" \\u6761\\u65B0\\u95FB\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Tag,{color:getCategoryColor(item._id),children:item.count})})]})})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],style:{marginTop:16},children:[/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u70ED\\u95E8\\u6570\\u636E\\u6E90\",style:{height:'400px'},bodyStyle:{height:'340px',overflow:'auto'},children:/*#__PURE__*/_jsx(List,{dataSource:(trendingStats===null||trendingStats===void 0?void 0:trendingStats.trendingSources)||[],renderItem:item=>/*#__PURE__*/_jsxs(List.Item,{style:{padding:'8px 0'},children:[/*#__PURE__*/_jsx(List.Item.Meta,{title:item._id.sourceName,description:\"\".concat(item.count,\" \\u6761\\u65B0\\u95FB\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:item.count})})]})})})}),/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsxs(Card,{title:\"\\u70ED\\u95E8\\u5173\\u952E\\u8BCD\\u60C5\\u611F\\u5206\\u6790\",style:{height:'400px'},bodyStyle:{height:'340px',overflow:'auto'},extra:(sentimentStats===null||sentimentStats===void 0?void 0:sentimentStats.overview)&&/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u603B\\u4F53\\u60C5\\u611F: \",sentimentStats.overview.totalScore,\"/10\"]}),children:[(sentimentStats===null||sentimentStats===void 0?void 0:sentimentStats.overview)&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:16,padding:12,background:'#f5f5f5',borderRadius:6},children:[/*#__PURE__*/_jsx(Text,{strong:true,children:\"\\u60C5\\u611F\\u6982\\u89C8\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:8},children:[/*#__PURE__*/_jsxs(Text,{children:[\"\\u603B\\u4F53\\u60C5\\u611F\\u6307\\u6570: \",sentimentStats.overview.totalScore,\"/10\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:4},children:[/*#__PURE__*/_jsxs(Tag,{color:\"green\",children:[\"\\u6B63\\u9762 \",sentimentStats.overview.positivePercentage,\"%\"]}),/*#__PURE__*/_jsxs(Tag,{color:\"default\",children:[\"\\u4E2D\\u6027 \",sentimentStats.overview.neutralPercentage,\"%\"]}),/*#__PURE__*/_jsxs(Tag,{color:\"red\",children:[\"\\u8D1F\\u9762 \",sentimentStats.overview.negativePercentage,\"%\"]})]})]})]}),/*#__PURE__*/_jsx(List,{dataSource:(sentimentStats===null||sentimentStats===void 0?void 0:sentimentStats.keywordSentiments)||(trendingStats===null||trendingStats===void 0?void 0:trendingStats.trendingKeywords)||[],renderItem:item=>{var _item$sentiment;return/*#__PURE__*/_jsxs(List.Item,{style:{padding:'8px 0'},children:[/*#__PURE__*/_jsx(List.Item.Meta,{title:/*#__PURE__*/_jsxs(Space,{children:[item.keyword||item._id,((_item$sentiment=item.sentiment)===null||_item$sentiment===void 0?void 0:_item$sentiment.emoji)&&/*#__PURE__*/_jsx(\"span\",{children:item.sentiment.emoji})]}),description:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsxs(Text,{type:\"secondary\",children:[\"\\u51FA\\u73B0 \",item.count,\" \\u6B21\"]}),item.sentiment&&/*#__PURE__*/_jsxs(Tag,{color:item.sentiment.sentiment==='positive'?'green':item.sentiment.sentiment==='negative'?'red':'default',children:[item.sentiment.sentiment==='positive'?'正面':item.sentiment.sentiment==='negative'?'负面':'中性',item.sentiment.score,\"\\u5206\"]})]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:item.count})})]});}})]})})]})]});};export default Dashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Statistic", "List", "Typography", "Tag", "Space", "Spin", "<PERSON><PERSON>", "FileTextOutlined", "DatabaseOutlined", "ClockCircleOutlined", "TrophyOutlined", "RiseOutlined", "PieChartOutlined", "BarChartOutlined", "LineChartOutlined", "Column", "Pie", "Line", "Area", "ReactECharts", "echarts", "axios", "moment", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "Dashboard", "_stats$byCategory", "_stats$bySource", "_trendingStats$trendi", "_stats$general", "_stats$bySource2", "_stats$byCategory2", "_stats$general2", "loading", "setLoading", "stats", "setStats", "recentNews", "setRecentNews", "trendingStats", "setTrendingStats", "timelineStats", "setTimelineStats", "sentimentStats", "setSentimentStats", "error", "setError", "fetchDashboardData", "statsRes", "newsRes", "trendingRes", "timelineRes", "sentimentRes", "Promise", "all", "get", "data", "items", "timeline", "console", "style", "textAlign", "padding", "children", "size", "marginTop", "message", "description", "type", "showIcon", "action", "onClick", "getCategoryColor", "category", "colors", "categoryPieData", "byCategory", "map", "item", "_id", "value", "count", "name", "sourceColumnData", "bySource", "slice", "source", "sourceName", "wordCloudData", "trendingKeywords", "log", "timelineChartData", "time<PERSON><PERSON><PERSON>", "hour", "undefined", "concat", "month", "toString", "padStart", "day", "week", "year", "time", "wordCloudOption", "backgroundColor", "tooltip", "show", "formatter", "params", "series", "gridSize", "sizeRange", "rotation<PERSON>ange", "rotationStep", "shape", "left", "top", "width", "height", "right", "bottom", "drawOutOfBound", "layoutAnimation", "maskImage", "keepAspect", "layoutAnimationDuration", "layoutAnimationEasing", "textStyle", "fontFamily", "fontWeight", "color", "maxValue", "Math", "max", "ratio", "emphasis", "focus", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "shadowOffsetX", "shadowOffsetY", "fontSize", "length", "categoryPieConfig", "appendPadding", "angleField", "colorField", "radius", "innerRadius", "label", "offset", "content", "fill", "legend", "position", "offsetX", "itemName", "datum", "statistic", "title", "whiteSpace", "overflow", "textOverflow", "reduce", "sum", "interactions", "sourceColumnConfig", "xField", "yField", "opacity", "xAxis", "autoHide", "autoRotate", "meta", "alias", "timelineConfig", "smooth", "point", "stroke", "lineWidth", "line", "area", "fillOpacity", "text", "grid", "lineDash", "yAxis", "min", "nice", "showMarkers", "animation", "appear", "duration", "level", "gutter", "marginBottom", "xs", "sm", "md", "general", "totalCount", "prefix", "valueStyle", "latestCollectTime", "fromNow", "lg", "extra", "_objectSpread", "option", "opts", "renderer", "notMerge", "lazyUpdate", "span", "href", "bodyStyle", "dataSource", "renderItem", "<PERSON><PERSON>", "Meta", "substring", "collectTime", "trendingCategories", "trendingSources", "overview", "totalScore", "background", "borderRadius", "strong", "positivePercentage", "neutralPercentage", "negativePercentage", "keywordSentiments", "_item$sentiment", "keyword", "sentiment", "emoji", "score"], "sources": ["D:/舆情监控/client/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, List, Typography, Tag, Space, Spin, Alert } from 'antd';\nimport {\n  FileTextOutlined,\n  DatabaseOutlined,\n  ClockCircleOutlined,\n  TrophyOutlined,\n  RiseOutlined,\n  PieChartOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  LineChartOutlined\n} from '@ant-design/icons';\nimport { Column, Pie, Line, Area } from '@ant-design/charts';\nimport ReactECharts from 'echarts-for-react';\nimport * as echarts from 'echarts';\nimport 'echarts-wordcloud';\nimport axios from 'axios';\nimport moment from 'moment';\n\nconst { Title, Text } = Typography;\n\nconst Dashboard = () => {\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState(null);\n  const [recentNews, setRecentNews] = useState([]);\n  const [trendingStats, setTrendingStats] = useState(null);\n  const [timelineStats, setTimelineStats] = useState([]);\n  const [sentimentStats, setSentimentStats] = useState(null);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const [statsRes, newsRes, trendingRes, timelineRes, sentimentRes] = await Promise.all([\n        axios.get('/api/stats'),\n        axios.get('/api/news?limit=10'),\n        axios.get('/api/stats/trending?period=day&limit=5'),\n        axios.get('/api/stats/timeline?period=day&days=7'),\n        axios.get('/api/stats/sentiment?period=day&days=7&limit=50')\n      ]);\n\n      setStats(statsRes.data.data);\n      setRecentNews(newsRes.data.data.items);\n      setTrendingStats(trendingRes.data.data);\n      setTimelineStats(timelineRes.data.data.timeline);\n      setSentimentStats(sentimentRes.data.data);\n    } catch (error) {\n      console.error('获取仪表板数据失败:', error);\n      setError('获取数据失败，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>加载中...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Alert\n        message=\"错误\"\n        description={error}\n        type=\"error\"\n        showIcon\n        action={\n          <button onClick={fetchDashboardData}>重试</button>\n        }\n      />\n    );\n  }\n\n  const getCategoryColor = (category) => {\n    const colors = {\n      '技术社区': 'blue',\n      '问答社区': 'green',\n      '社交媒体': 'orange',\n      '新闻媒体': 'red',\n      '财经媒体': 'purple',\n      '科技媒体': 'cyan',\n      '视频平台': 'magenta',\n      '短视频': 'lime',\n      '投资社区': 'gold'\n    };\n    return colors[category] || 'default';\n  };\n\n  // 准备分类饼图数据\n  const categoryPieData = stats?.byCategory?.map(item => ({\n    type: item._id,\n    value: item.count,\n    name: item._id\n  })) || [];\n\n  // 准备数据源柱状图数据\n  const sourceColumnData = stats?.bySource?.slice(0, 10).map(item => ({\n    source: item._id.sourceName,\n    count: item.count\n  })) || [];\n\n  // 准备词云数据\n  const wordCloudData = trendingStats?.trendingKeywords?.map(item => ({\n    name: item._id,\n    value: item.count\n  })) || [];\n\n  // 调试输出\n  console.log('词云数据:', wordCloudData);\n  console.log('热门统计数据:', trendingStats);\n\n  // 准备时间线图表数据\n  const timelineChartData = timelineStats.map(item => {\n    const { _id } = item;\n    let timeLabel;\n    if (_id.hour !== undefined) {\n      timeLabel = `${_id.month.toString().padStart(2, '0')}-${_id.day.toString().padStart(2, '0')} ${_id.hour}:00`;\n    } else if (_id.day !== undefined) {\n      timeLabel = `${_id.month.toString().padStart(2, '0')}-${_id.day.toString().padStart(2, '0')}`;\n    } else if (_id.week !== undefined) {\n      timeLabel = `第${_id.week}周`;\n    } else {\n      timeLabel = `${_id.year}-${_id.month.toString().padStart(2, '0')}`;\n    }\n    return {\n      time: timeLabel,\n      count: item.count\n    };\n  });\n\n  // 词云配置\n  const wordCloudOption = {\n    backgroundColor: 'transparent',\n    tooltip: {\n      show: true,\n      formatter: function (params) {\n        return `${params.name}: ${params.value} 次`;\n      }\n    },\n    series: [{\n      type: 'wordCloud',\n      // 高级布局优化参数\n      gridSize: 12,  // 进一步增加网格大小，确保词语不重叠\n      sizeRange: [18, 45],  // 调整字体大小范围，减小最大字体避免过度拥挤\n      rotationRange: [0, 0],  // 保持水平排列，避免旋转造成的混乱\n      rotationStep: 0,\n      shape: 'circle',  // 使用圆形布局，更加紧凑\n      left: 'center',\n      top: 'center',\n      width: '85%',  // 进一步缩小显示区域，增加边距\n      height: '85%',\n      right: null,\n      bottom: null,\n      drawOutOfBound: false,\n      layoutAnimation: true,\n      // 布局算法优化\n      maskImage: null,\n      keepAspect: true,  // 保持宽高比，避免变形\n      // 词语间距优化\n      padding: 8,  // 增加词语间距\n      // 布局尝试次数，提高布局质量\n      layoutAnimationDuration: 1000,\n      layoutAnimationEasing: 'cubicOut',\n      textStyle: {\n        fontFamily: 'Microsoft YaHei, PingFang SC, Helvetica Neue, Arial, sans-serif',\n        fontWeight: '600',  // 使用半粗体，平衡清晰度和美观\n        color: function (params) {\n          // 根据词频大小使用渐变色系\n          const value = params.value;\n          const maxValue = Math.max(...(wordCloudData || []).map(item => item.value));\n          const ratio = value / maxValue;\n\n          // 使用蓝色系渐变，高频词颜色更深\n          if (ratio > 0.8) {\n            return '#1890ff';  // 深蓝\n          } else if (ratio > 0.6) {\n            return '#40a9ff';  // 中蓝\n          } else if (ratio > 0.4) {\n            return '#69c0ff';  // 浅蓝\n          } else if (ratio > 0.2) {\n            return '#91d5ff';  // 很浅蓝\n          } else {\n            return '#bae7ff';  // 极浅蓝\n          }\n        }\n      },\n      emphasis: {\n        focus: 'self',\n        textStyle: {\n          shadowBlur: 6,\n          shadowColor: 'rgba(24, 144, 255, 0.4)',\n          shadowOffsetX: 1,\n          shadowOffsetY: 1,\n          fontSize: function(params) {\n            // hover时字体稍微放大\n            return params.fontSize * 1.1;\n          }\n        }\n      },\n      data: wordCloudData && wordCloudData.length > 0 ? wordCloudData : [\n        { name: '暂无数据', value: 1 }\n      ]\n    }]\n  };\n\n  // 分类饼图配置\n  const categoryPieConfig = {\n    appendPadding: 10,\n    data: categoryPieData,\n    angleField: 'value',\n    colorField: 'type',\n    radius: 0.75,\n    innerRadius: 0.3,\n    label: {\n      type: 'inner',\n      offset: '-30%',\n      content: function(data) {\n        return data.value > 20 ? `${data.type}\\n${data.value}` : '';\n      },\n      style: {\n        fontSize: 11,\n        textAlign: 'center',\n        fill: 'white',\n        fontWeight: 'bold'\n      }\n    },\n    legend: {\n      position: 'right',\n      offsetX: -20,\n      itemName: {\n        style: {\n          fontSize: 12\n        }\n      }\n    },\n    tooltip: {\n      formatter: (datum) => {\n        return { name: datum.type, value: `${datum.value} 条` };\n      }\n    },\n    statistic: {\n      title: {\n        style: {\n          whiteSpace: 'pre-wrap',\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n        },\n        content: '总计',\n      },\n      content: {\n        style: {\n          whiteSpace: 'pre-wrap',\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n        },\n        content: categoryPieData.reduce((sum, item) => sum + item.value, 0).toString(),\n      },\n    },\n    interactions: [\n      {\n        type: 'element-selected',\n      },\n      {\n        type: 'element-active',\n      },\n    ],\n  };\n\n  // 数据源柱状图配置\n  const sourceColumnConfig = {\n    data: sourceColumnData,\n    xField: 'source',\n    yField: 'count',\n    label: {\n      position: 'middle',\n      style: {\n        fill: '#FFFFFF',\n        opacity: 0.6,\n      },\n    },\n    xAxis: {\n      label: {\n        autoHide: true,\n        autoRotate: false,\n      },\n    },\n    meta: {\n      source: {\n        alias: '数据源',\n      },\n      count: {\n        alias: '新闻数量',\n      },\n    },\n  };\n\n  // 时间线图表配置\n  const timelineConfig = {\n    data: timelineChartData,\n    xField: 'time',\n    yField: 'count',\n    smooth: true,\n    padding: [20, 20, 50, 60], // 增加左侧padding确保Y轴显示\n    point: {\n      size: 4,\n      shape: 'circle',\n      style: {\n        fill: 'white',\n        stroke: '#1890ff',\n        lineWidth: 2,\n      },\n    },\n    line: {\n      style: {\n        stroke: '#1890ff',\n        lineWidth: 3,\n      },\n    },\n    area: {\n      style: {\n        fill: 'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff',\n        fillOpacity: 0.3,\n      },\n    },\n    xAxis: {\n      position: 'bottom',\n      label: {\n        autoRotate: true,\n        style: {\n          fontSize: 12,\n        },\n      },\n      title: {\n        text: '时间',\n        position: 'end',\n        style: {\n          fontSize: 14,\n          fontWeight: 'bold',\n        },\n      },\n      grid: {\n        line: {\n          style: {\n            stroke: '#f0f0f0',\n            lineWidth: 1,\n            lineDash: [4, 5],\n          },\n        },\n      },\n    },\n    yAxis: {\n      position: 'left',\n      min: 0,\n      nice: true,\n      label: {\n        style: {\n          fontSize: 12,\n        },\n      },\n      title: {\n        text: '更新数量',\n        position: 'end',\n        style: {\n          fontSize: 14,\n          fontWeight: 'bold',\n        },\n      },\n      grid: {\n        line: {\n          style: {\n            stroke: '#f0f0f0',\n            lineWidth: 1,\n            lineDash: [4, 5],\n          },\n        },\n      },\n    },\n    tooltip: {\n      showMarkers: true,\n      formatter: (datum) => {\n        return { name: '更新数量', value: `${datum.count} 条` };\n      },\n    },\n    animation: {\n      appear: {\n        animation: 'path-in',\n        duration: 1000,\n      },\n    },\n  };\n\n  return (\n    <div>\n      <Title level={2}>仪表板</Title>\n      \n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总新闻数量\"\n              value={stats?.general?.totalCount || 0}\n              prefix={<FileTextOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"数据源数量\"\n              value={stats?.bySource?.length || 0}\n              prefix={<DatabaseOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"分类数量\"\n              value={stats?.byCategory?.length || 0}\n              prefix={<TrophyOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"最新更新\"\n              value={stats?.general?.latestCollectTime ? \n                moment(stats.general.latestCollectTime).fromNow() : '暂无数据'}\n              prefix={<ClockCircleOutlined />}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 图表展示区域 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        {/* 分类分布饼图 */}\n        <Col xs={24} lg={12}>\n          <Card title=\"分类分布\" extra={<PieChartOutlined />}>\n            {categoryPieData.length > 0 ? (\n              <Pie {...categoryPieConfig} height={300} />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin />\n                <div style={{ marginTop: 16 }}>加载中...</div>\n              </div>\n            )}\n          </Card>\n        </Col>\n\n        {/* 热门关键词词云 */}\n        <Col xs={24} lg={12}>\n          <Card title=\"热门关键词词云\" extra={<RiseOutlined />}>\n            {trendingStats && trendingStats.trendingKeywords ? (\n              <ReactECharts\n                option={wordCloudOption}\n                style={{ height: '300px' }}\n                opts={{ renderer: 'canvas' }}\n                notMerge={true}\n                lazyUpdate={true}\n              />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin />\n                <div style={{ marginTop: 16 }}>加载中...</div>\n              </div>\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 时间线趋势图 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col span={24}>\n          <Card title=\"数据更新趋势\" extra={<LineChartOutlined />}>\n            {timelineChartData.length > 0 ? (\n              <Area {...timelineConfig} height={300} />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin />\n                <div style={{ marginTop: 16 }}>加载中...</div>\n              </div>\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 数据源统计柱状图 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col span={24}>\n          <Card title=\"热门数据源统计\" extra={<BarChartOutlined />}>\n            {sourceColumnData.length > 0 ? (\n              <Column {...sourceColumnConfig} height={300} />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin />\n                <div style={{ marginTop: 16 }}>加载中...</div>\n              </div>\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]}>\n        {/* 最新新闻 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title=\"最新新闻\"\n            extra={<a href=\"/news\">查看更多</a>}\n            style={{ height: '400px' }}\n            bodyStyle={{ height: '340px', overflow: 'auto' }}\n          >\n            <List\n              dataSource={recentNews}\n              renderItem={(item) => (\n                <List.Item style={{ padding: '8px 0' }}>\n                  <List.Item.Meta\n                    title={\n                      <a href={`/news/${item._id}`} style={{ fontSize: 14 }}>\n                        {item.title.length > 50 ? `${item.title.substring(0, 50)}...` : item.title}\n                      </a>\n                    }\n                    description={\n                      <Space>\n                        <Tag color={getCategoryColor(item.category)} size=\"small\">\n                          {item.category}\n                        </Tag>\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          {item.sourceName}\n                        </Text>\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          {moment(item.collectTime).fromNow()}\n                        </Text>\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n\n        {/* 热门分类列表 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title=\"热门分类\"\n            extra={<RiseOutlined />}\n            style={{ height: '400px' }}\n            bodyStyle={{ height: '340px', overflow: 'auto' }}\n          >\n            <List\n              dataSource={trendingStats?.trendingCategories || []}\n              renderItem={(item) => (\n                <List.Item style={{ padding: '8px 0' }}>\n                  <List.Item.Meta\n                    title={item._id}\n                    description={`${item.count} 条新闻`}\n                  />\n                  <div>\n                    <Tag color={getCategoryColor(item._id)}>\n                      {item.count}\n                    </Tag>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>\n        {/* 热门数据源 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title=\"热门数据源\"\n            style={{ height: '400px' }}\n            bodyStyle={{ height: '340px', overflow: 'auto' }}\n          >\n            <List\n              dataSource={trendingStats?.trendingSources || []}\n              renderItem={(item) => (\n                <List.Item style={{ padding: '8px 0' }}>\n                  <List.Item.Meta\n                    title={item._id.sourceName}\n                    description={`${item.count} 条新闻`}\n                  />\n                  <div>\n                    <Tag color=\"blue\">{item.count}</Tag>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n\n        {/* 热门关键词（带情感分析） */}\n        <Col xs={24} lg={12}>\n          <Card\n            title=\"热门关键词情感分析\"\n            style={{ height: '400px' }}\n            bodyStyle={{ height: '340px', overflow: 'auto' }}\n            extra={\n              sentimentStats?.overview && (\n                <Text type=\"secondary\">\n                  总体情感: {sentimentStats.overview.totalScore}/10\n                </Text>\n              )\n            }\n          >\n            {sentimentStats?.overview && (\n              <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>\n                <Text strong>情感概览</Text>\n                <div style={{ marginTop: 8 }}>\n                  <Text>总体情感指数: {sentimentStats.overview.totalScore}/10</Text>\n                  <div style={{ marginTop: 4 }}>\n                    <Tag color=\"green\">正面 {sentimentStats.overview.positivePercentage}%</Tag>\n                    <Tag color=\"default\">中性 {sentimentStats.overview.neutralPercentage}%</Tag>\n                    <Tag color=\"red\">负面 {sentimentStats.overview.negativePercentage}%</Tag>\n                  </div>\n                </div>\n              </div>\n            )}\n            <List\n              dataSource={sentimentStats?.keywordSentiments || trendingStats?.trendingKeywords || []}\n              renderItem={(item) => (\n                <List.Item style={{ padding: '8px 0' }}>\n                  <List.Item.Meta\n                    title={\n                      <Space>\n                        {item.keyword || item._id}\n                        {item.sentiment?.emoji && <span>{item.sentiment.emoji}</span>}\n                      </Space>\n                    }\n                    description={\n                      <Space>\n                        <Text type=\"secondary\">出现 {item.count} 次</Text>\n                        {item.sentiment && (\n                          <Tag color={\n                            item.sentiment.sentiment === 'positive' ? 'green' :\n                            item.sentiment.sentiment === 'negative' ? 'red' : 'default'\n                          }>\n                            {item.sentiment.sentiment === 'positive' ? '正面' :\n                             item.sentiment.sentiment === 'negative' ? '负面' : '中性'}\n                            {item.sentiment.score}分\n                          </Tag>\n                        )}\n                      </Space>\n                    }\n                  />\n                  <div>\n                    <Tag color=\"blue\">{item.count}</Tag>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": "uHAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,SAAS,CAAEC,IAAI,CAAEC,UAAU,CAAEC,GAAG,CAAEC,KAAK,CAAEC,IAAI,CAAEC,KAAK,KAAQ,MAAM,CAC3F,OACEC,gBAAgB,CAChBC,gBAAgB,CAChBC,mBAAmB,CACnBC,cAAc,CACdC,YAAY,CACZC,gBAAgB,CAChBC,gBAAgB,CAChBC,iBAAiB,KACZ,mBAAmB,CAC1B,OAASC,MAAM,CAAEC,GAAG,CAAEC,IAAI,CAAEC,IAAI,KAAQ,oBAAoB,CAC5D,MAAO,CAAAC,YAAY,KAAM,mBAAmB,CAC5C,MAAO,GAAK,CAAAC,OAAO,KAAM,SAAS,CAClC,MAAO,mBAAmB,CAC1B,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,QAAQ,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5B,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG1B,UAAU,CAElC,KAAM,CAAA2B,SAAS,CAAGA,CAAA,GAAM,KAAAC,iBAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,cAAA,CAAAC,gBAAA,CAAAC,kBAAA,CAAAC,eAAA,CACtB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG3C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4C,KAAK,CAAEC,QAAQ,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAC8C,UAAU,CAAEC,aAAa,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACgD,aAAa,CAAEC,gBAAgB,CAAC,CAAGjD,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACkD,aAAa,CAAEC,gBAAgB,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACoD,cAAc,CAAEC,iBAAiB,CAAC,CAAGrD,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACsD,KAAK,CAAEC,QAAQ,CAAC,CAAGvD,QAAQ,CAAC,IAAI,CAAC,CAExCC,SAAS,CAAC,IAAM,CACduD,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACFb,UAAU,CAAC,IAAI,CAAC,CAChBY,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAACE,QAAQ,CAAEC,OAAO,CAAEC,WAAW,CAAEC,WAAW,CAAEC,YAAY,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACpFrC,KAAK,CAACsC,GAAG,CAAC,YAAY,CAAC,CACvBtC,KAAK,CAACsC,GAAG,CAAC,oBAAoB,CAAC,CAC/BtC,KAAK,CAACsC,GAAG,CAAC,wCAAwC,CAAC,CACnDtC,KAAK,CAACsC,GAAG,CAAC,uCAAuC,CAAC,CAClDtC,KAAK,CAACsC,GAAG,CAAC,iDAAiD,CAAC,CAC7D,CAAC,CAEFnB,QAAQ,CAACY,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC,CAC5BlB,aAAa,CAACW,OAAO,CAACO,IAAI,CAACA,IAAI,CAACC,KAAK,CAAC,CACtCjB,gBAAgB,CAACU,WAAW,CAACM,IAAI,CAACA,IAAI,CAAC,CACvCd,gBAAgB,CAACS,WAAW,CAACK,IAAI,CAACA,IAAI,CAACE,QAAQ,CAAC,CAChDd,iBAAiB,CAACQ,YAAY,CAACI,IAAI,CAACA,IAAI,CAAC,CAC3C,CAAE,MAAOX,KAAK,CAAE,CACdc,OAAO,CAACd,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClCC,QAAQ,CAAC,cAAc,CAAC,CAC1B,CAAC,OAAS,CACRZ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAID,OAAO,CAAE,CACX,mBACEX,KAAA,QAAKsC,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnD3C,IAAA,CAACnB,IAAI,EAAC+D,IAAI,CAAC,OAAO,CAAE,CAAC,cACrB5C,IAAA,QAAKwC,KAAK,CAAE,CAAEK,SAAS,CAAE,EAAG,CAAE,CAAAF,QAAA,CAAC,uBAAM,CAAK,CAAC,EACxC,CAAC,CAEV,CAEA,GAAIlB,KAAK,CAAE,CACT,mBACEzB,IAAA,CAAClB,KAAK,EACJgE,OAAO,CAAC,cAAI,CACZC,WAAW,CAAEtB,KAAM,CACnBuB,IAAI,CAAC,OAAO,CACZC,QAAQ,MACRC,MAAM,cACJlD,IAAA,WAAQmD,OAAO,CAAExB,kBAAmB,CAAAgB,QAAA,CAAC,cAAE,CAAQ,CAChD,CACF,CAAC,CAEN,CAEA,KAAM,CAAAS,gBAAgB,CAAIC,QAAQ,EAAK,CACrC,KAAM,CAAAC,MAAM,CAAG,CACb,MAAM,CAAE,MAAM,CACd,MAAM,CAAE,OAAO,CACf,MAAM,CAAE,QAAQ,CAChB,MAAM,CAAE,KAAK,CACb,MAAM,CAAE,QAAQ,CAChB,MAAM,CAAE,MAAM,CACd,MAAM,CAAE,SAAS,CACjB,KAAK,CAAE,MAAM,CACb,MAAM,CAAE,MACV,CAAC,CACD,MAAO,CAAAA,MAAM,CAACD,QAAQ,CAAC,EAAI,SAAS,CACtC,CAAC,CAED;AACA,KAAM,CAAAE,eAAe,CAAG,CAAAxC,KAAK,SAALA,KAAK,kBAAAT,iBAAA,CAALS,KAAK,CAAEyC,UAAU,UAAAlD,iBAAA,iBAAjBA,iBAAA,CAAmBmD,GAAG,CAACC,IAAI,GAAK,CACtDV,IAAI,CAAEU,IAAI,CAACC,GAAG,CACdC,KAAK,CAAEF,IAAI,CAACG,KAAK,CACjBC,IAAI,CAAEJ,IAAI,CAACC,GACb,CAAC,CAAC,CAAC,GAAI,EAAE,CAET;AACA,KAAM,CAAAI,gBAAgB,CAAG,CAAAhD,KAAK,SAALA,KAAK,kBAAAR,eAAA,CAALQ,KAAK,CAAEiD,QAAQ,UAAAzD,eAAA,iBAAfA,eAAA,CAAiB0D,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAACR,GAAG,CAACC,IAAI,GAAK,CAClEQ,MAAM,CAAER,IAAI,CAACC,GAAG,CAACQ,UAAU,CAC3BN,KAAK,CAAEH,IAAI,CAACG,KACd,CAAC,CAAC,CAAC,GAAI,EAAE,CAET;AACA,KAAM,CAAAO,aAAa,CAAG,CAAAjD,aAAa,SAAbA,aAAa,kBAAAX,qBAAA,CAAbW,aAAa,CAAEkD,gBAAgB,UAAA7D,qBAAA,iBAA/BA,qBAAA,CAAiCiD,GAAG,CAACC,IAAI,GAAK,CAClEI,IAAI,CAAEJ,IAAI,CAACC,GAAG,CACdC,KAAK,CAAEF,IAAI,CAACG,KACd,CAAC,CAAC,CAAC,GAAI,EAAE,CAET;AACAtB,OAAO,CAAC+B,GAAG,CAAC,OAAO,CAAEF,aAAa,CAAC,CACnC7B,OAAO,CAAC+B,GAAG,CAAC,SAAS,CAAEnD,aAAa,CAAC,CAErC;AACA,KAAM,CAAAoD,iBAAiB,CAAGlD,aAAa,CAACoC,GAAG,CAACC,IAAI,EAAI,CAClD,KAAM,CAAEC,GAAI,CAAC,CAAGD,IAAI,CACpB,GAAI,CAAAc,SAAS,CACb,GAAIb,GAAG,CAACc,IAAI,GAAKC,SAAS,CAAE,CAC1BF,SAAS,IAAAG,MAAA,CAAMhB,GAAG,CAACiB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAAH,MAAA,CAAIhB,GAAG,CAACoB,GAAG,CAACF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAAH,MAAA,CAAIhB,GAAG,CAACc,IAAI,OAAK,CAC9G,CAAC,IAAM,IAAId,GAAG,CAACoB,GAAG,GAAKL,SAAS,CAAE,CAChCF,SAAS,IAAAG,MAAA,CAAMhB,GAAG,CAACiB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAAH,MAAA,CAAIhB,GAAG,CAACoB,GAAG,CAACF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAAE,CAC/F,CAAC,IAAM,IAAInB,GAAG,CAACqB,IAAI,GAAKN,SAAS,CAAE,CACjCF,SAAS,UAAAG,MAAA,CAAOhB,GAAG,CAACqB,IAAI,UAAG,CAC7B,CAAC,IAAM,CACLR,SAAS,IAAAG,MAAA,CAAMhB,GAAG,CAACsB,IAAI,MAAAN,MAAA,CAAIhB,GAAG,CAACiB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAAE,CACpE,CACA,MAAO,CACLI,IAAI,CAAEV,SAAS,CACfX,KAAK,CAAEH,IAAI,CAACG,KACd,CAAC,CACH,CAAC,CAAC,CAEF;AACA,KAAM,CAAAsB,eAAe,CAAG,CACtBC,eAAe,CAAE,aAAa,CAC9BC,OAAO,CAAE,CACPC,IAAI,CAAE,IAAI,CACVC,SAAS,CAAE,QAAAA,CAAUC,MAAM,CAAE,CAC3B,SAAAb,MAAA,CAAUa,MAAM,CAAC1B,IAAI,OAAAa,MAAA,CAAKa,MAAM,CAAC5B,KAAK,YACxC,CACF,CAAC,CACD6B,MAAM,CAAE,CAAC,CACPzC,IAAI,CAAE,WAAW,CACjB;AACA0C,QAAQ,CAAE,EAAE,CAAG;AACfC,SAAS,CAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAG;AACtBC,aAAa,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CAAG;AACxBC,YAAY,CAAE,CAAC,CACfC,KAAK,CAAE,QAAQ,CAAG;AAClBC,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,KAAK,CAAG;AACfC,MAAM,CAAE,KAAK,CACbC,KAAK,CAAE,IAAI,CACXC,MAAM,CAAE,IAAI,CACZC,cAAc,CAAE,KAAK,CACrBC,eAAe,CAAE,IAAI,CACrB;AACAC,SAAS,CAAE,IAAI,CACfC,UAAU,CAAE,IAAI,CAAG;AACnB;AACA9D,OAAO,CAAE,CAAC,CAAG;AACb;AACA+D,uBAAuB,CAAE,IAAI,CAC7BC,qBAAqB,CAAE,UAAU,CACjCC,SAAS,CAAE,CACTC,UAAU,CAAE,iEAAiE,CAC7EC,UAAU,CAAE,KAAK,CAAG;AACpBC,KAAK,CAAE,QAAAA,CAAUtB,MAAM,CAAE,CACvB;AACA,KAAM,CAAA5B,KAAK,CAAG4B,MAAM,CAAC5B,KAAK,CAC1B,KAAM,CAAAmD,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,CAAC7C,aAAa,EAAI,EAAE,EAAEX,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACE,KAAK,CAAC,CAAC,CAC3E,KAAM,CAAAsD,KAAK,CAAGtD,KAAK,CAAGmD,QAAQ,CAE9B;AACA,GAAIG,KAAK,CAAG,GAAG,CAAE,CACf,MAAO,SAAS,CAAG;AACrB,CAAC,IAAM,IAAIA,KAAK,CAAG,GAAG,CAAE,CACtB,MAAO,SAAS,CAAG;AACrB,CAAC,IAAM,IAAIA,KAAK,CAAG,GAAG,CAAE,CACtB,MAAO,SAAS,CAAG;AACrB,CAAC,IAAM,IAAIA,KAAK,CAAG,GAAG,CAAE,CACtB,MAAO,SAAS,CAAG;AACrB,CAAC,IAAM,CACL,MAAO,SAAS,CAAG;AACrB,CACF,CACF,CAAC,CACDC,QAAQ,CAAE,CACRC,KAAK,CAAE,MAAM,CACbT,SAAS,CAAE,CACTU,UAAU,CAAE,CAAC,CACbC,WAAW,CAAE,yBAAyB,CACtCC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,CAAC,CAChBC,QAAQ,CAAE,QAAAA,CAASjC,MAAM,CAAE,CACzB;AACA,MAAO,CAAAA,MAAM,CAACiC,QAAQ,CAAG,GAAG,CAC9B,CACF,CACF,CAAC,CACDrF,IAAI,CAAEgC,aAAa,EAAIA,aAAa,CAACsD,MAAM,CAAG,CAAC,CAAGtD,aAAa,CAAG,CAChE,CAAEN,IAAI,CAAE,MAAM,CAAEF,KAAK,CAAE,CAAE,CAAC,CAE9B,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAA+D,iBAAiB,CAAG,CACxBC,aAAa,CAAE,EAAE,CACjBxF,IAAI,CAAEmB,eAAe,CACrBsE,UAAU,CAAE,OAAO,CACnBC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE,IAAI,CACZC,WAAW,CAAE,GAAG,CAChBC,KAAK,CAAE,CACLjF,IAAI,CAAE,OAAO,CACbkF,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,QAAAA,CAAS/F,IAAI,CAAE,CACtB,MAAO,CAAAA,IAAI,CAACwB,KAAK,CAAG,EAAE,IAAAe,MAAA,CAAMvC,IAAI,CAACY,IAAI,OAAA2B,MAAA,CAAKvC,IAAI,CAACwB,KAAK,EAAK,EAAE,CAC7D,CAAC,CACDpB,KAAK,CAAE,CACLiF,QAAQ,CAAE,EAAE,CACZhF,SAAS,CAAE,QAAQ,CACnB2F,IAAI,CAAE,OAAO,CACbvB,UAAU,CAAE,MACd,CACF,CAAC,CACDwB,MAAM,CAAE,CACNC,QAAQ,CAAE,OAAO,CACjBC,OAAO,CAAE,CAAC,EAAE,CACZC,QAAQ,CAAE,CACRhG,KAAK,CAAE,CACLiF,QAAQ,CAAE,EACZ,CACF,CACF,CAAC,CACDpC,OAAO,CAAE,CACPE,SAAS,CAAGkD,KAAK,EAAK,CACpB,MAAO,CAAE3E,IAAI,CAAE2E,KAAK,CAACzF,IAAI,CAAEY,KAAK,IAAAe,MAAA,CAAK8D,KAAK,CAAC7E,KAAK,WAAK,CAAC,CACxD,CACF,CAAC,CACD8E,SAAS,CAAE,CACTC,KAAK,CAAE,CACLnG,KAAK,CAAE,CACLoG,UAAU,CAAE,UAAU,CACtBC,QAAQ,CAAE,QAAQ,CAClBC,YAAY,CAAE,UAChB,CAAC,CACDX,OAAO,CAAE,IACX,CAAC,CACDA,OAAO,CAAE,CACP3F,KAAK,CAAE,CACLoG,UAAU,CAAE,UAAU,CACtBC,QAAQ,CAAE,QAAQ,CAClBC,YAAY,CAAE,UAChB,CAAC,CACDX,OAAO,CAAE5E,eAAe,CAACwF,MAAM,CAAC,CAACC,GAAG,CAAEtF,IAAI,GAAKsF,GAAG,CAAGtF,IAAI,CAACE,KAAK,CAAE,CAAC,CAAC,CAACiB,QAAQ,CAAC,CAC/E,CACF,CAAC,CACDoE,YAAY,CAAE,CACZ,CACEjG,IAAI,CAAE,kBACR,CAAC,CACD,CACEA,IAAI,CAAE,gBACR,CAAC,CAEL,CAAC,CAED;AACA,KAAM,CAAAkG,kBAAkB,CAAG,CACzB9G,IAAI,CAAE2B,gBAAgB,CACtBoF,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,OAAO,CACfnB,KAAK,CAAE,CACLK,QAAQ,CAAE,QAAQ,CAClB9F,KAAK,CAAE,CACL4F,IAAI,CAAE,SAAS,CACfiB,OAAO,CAAE,GACX,CACF,CAAC,CACDC,KAAK,CAAE,CACLrB,KAAK,CAAE,CACLsB,QAAQ,CAAE,IAAI,CACdC,UAAU,CAAE,KACd,CACF,CAAC,CACDC,IAAI,CAAE,CACJvF,MAAM,CAAE,CACNwF,KAAK,CAAE,KACT,CAAC,CACD7F,KAAK,CAAE,CACL6F,KAAK,CAAE,MACT,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAG,CACrBvH,IAAI,CAAEmC,iBAAiB,CACvB4E,MAAM,CAAE,MAAM,CACdC,MAAM,CAAE,OAAO,CACfQ,MAAM,CAAE,IAAI,CACZlH,OAAO,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAE;AAC3BmH,KAAK,CAAE,CACLjH,IAAI,CAAE,CAAC,CACPkD,KAAK,CAAE,QAAQ,CACftD,KAAK,CAAE,CACL4F,IAAI,CAAE,OAAO,CACb0B,MAAM,CAAE,SAAS,CACjBC,SAAS,CAAE,CACb,CACF,CAAC,CACDC,IAAI,CAAE,CACJxH,KAAK,CAAE,CACLsH,MAAM,CAAE,SAAS,CACjBC,SAAS,CAAE,CACb,CACF,CAAC,CACDE,IAAI,CAAE,CACJzH,KAAK,CAAE,CACL4F,IAAI,CAAE,wCAAwC,CAC9C8B,WAAW,CAAE,GACf,CACF,CAAC,CACDZ,KAAK,CAAE,CACLhB,QAAQ,CAAE,QAAQ,CAClBL,KAAK,CAAE,CACLuB,UAAU,CAAE,IAAI,CAChBhH,KAAK,CAAE,CACLiF,QAAQ,CAAE,EACZ,CACF,CAAC,CACDkB,KAAK,CAAE,CACLwB,IAAI,CAAE,IAAI,CACV7B,QAAQ,CAAE,KAAK,CACf9F,KAAK,CAAE,CACLiF,QAAQ,CAAE,EAAE,CACZZ,UAAU,CAAE,MACd,CACF,CAAC,CACDuD,IAAI,CAAE,CACJJ,IAAI,CAAE,CACJxH,KAAK,CAAE,CACLsH,MAAM,CAAE,SAAS,CACjBC,SAAS,CAAE,CAAC,CACZM,QAAQ,CAAE,CAAC,CAAC,CAAE,CAAC,CACjB,CACF,CACF,CACF,CAAC,CACDC,KAAK,CAAE,CACLhC,QAAQ,CAAE,MAAM,CAChBiC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,IAAI,CACVvC,KAAK,CAAE,CACLzF,KAAK,CAAE,CACLiF,QAAQ,CAAE,EACZ,CACF,CAAC,CACDkB,KAAK,CAAE,CACLwB,IAAI,CAAE,MAAM,CACZ7B,QAAQ,CAAE,KAAK,CACf9F,KAAK,CAAE,CACLiF,QAAQ,CAAE,EAAE,CACZZ,UAAU,CAAE,MACd,CACF,CAAC,CACDuD,IAAI,CAAE,CACJJ,IAAI,CAAE,CACJxH,KAAK,CAAE,CACLsH,MAAM,CAAE,SAAS,CACjBC,SAAS,CAAE,CAAC,CACZM,QAAQ,CAAE,CAAC,CAAC,CAAE,CAAC,CACjB,CACF,CACF,CACF,CAAC,CACDhF,OAAO,CAAE,CACPoF,WAAW,CAAE,IAAI,CACjBlF,SAAS,CAAGkD,KAAK,EAAK,CACpB,MAAO,CAAE3E,IAAI,CAAE,MAAM,CAAEF,KAAK,IAAAe,MAAA,CAAK8D,KAAK,CAAC5E,KAAK,WAAK,CAAC,CACpD,CACF,CAAC,CACD6G,SAAS,CAAE,CACTC,MAAM,CAAE,CACND,SAAS,CAAE,SAAS,CACpBE,QAAQ,CAAE,IACZ,CACF,CACF,CAAC,CAED,mBACE1K,KAAA,QAAAyC,QAAA,eACE3C,IAAA,CAACG,KAAK,EAAC0K,KAAK,CAAE,CAAE,CAAAlI,QAAA,CAAC,oBAAG,CAAO,CAAC,cAG5BzC,KAAA,CAAC7B,GAAG,EAACyM,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACtI,KAAK,CAAE,CAAEuI,YAAY,CAAE,EAAG,CAAE,CAAApI,QAAA,eACjD3C,IAAA,CAAC1B,GAAG,EAAC0M,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAvI,QAAA,cACzB3C,IAAA,CAACzB,IAAI,EAAAoE,QAAA,cACH3C,IAAA,CAACxB,SAAS,EACRmK,KAAK,CAAC,gCAAO,CACb/E,KAAK,CAAE,CAAA7C,KAAK,SAALA,KAAK,kBAAAN,cAAA,CAALM,KAAK,CAAEoK,OAAO,UAAA1K,cAAA,iBAAdA,cAAA,CAAgB2K,UAAU,GAAI,CAAE,CACvCC,MAAM,cAAErL,IAAA,CAACjB,gBAAgB,GAAE,CAAE,CAC7BuM,UAAU,CAAE,CAAExE,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN9G,IAAA,CAAC1B,GAAG,EAAC0M,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAvI,QAAA,cACzB3C,IAAA,CAACzB,IAAI,EAAAoE,QAAA,cACH3C,IAAA,CAACxB,SAAS,EACRmK,KAAK,CAAC,gCAAO,CACb/E,KAAK,CAAE,CAAA7C,KAAK,SAALA,KAAK,kBAAAL,gBAAA,CAALK,KAAK,CAAEiD,QAAQ,UAAAtD,gBAAA,iBAAfA,gBAAA,CAAiBgH,MAAM,GAAI,CAAE,CACpC2D,MAAM,cAAErL,IAAA,CAAChB,gBAAgB,GAAE,CAAE,CAC7BsM,UAAU,CAAE,CAAExE,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN9G,IAAA,CAAC1B,GAAG,EAAC0M,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAvI,QAAA,cACzB3C,IAAA,CAACzB,IAAI,EAAAoE,QAAA,cACH3C,IAAA,CAACxB,SAAS,EACRmK,KAAK,CAAC,0BAAM,CACZ/E,KAAK,CAAE,CAAA7C,KAAK,SAALA,KAAK,kBAAAJ,kBAAA,CAALI,KAAK,CAAEyC,UAAU,UAAA7C,kBAAA,iBAAjBA,kBAAA,CAAmB+G,MAAM,GAAI,CAAE,CACtC2D,MAAM,cAAErL,IAAA,CAACd,cAAc,GAAE,CAAE,CAC3BoM,UAAU,CAAE,CAAExE,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,cACN9G,IAAA,CAAC1B,GAAG,EAAC0M,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAvI,QAAA,cACzB3C,IAAA,CAACzB,IAAI,EAAAoE,QAAA,cACH3C,IAAA,CAACxB,SAAS,EACRmK,KAAK,CAAC,0BAAM,CACZ/E,KAAK,CAAE7C,KAAK,SAALA,KAAK,YAAAH,eAAA,CAALG,KAAK,CAAEoK,OAAO,UAAAvK,eAAA,WAAdA,eAAA,CAAgB2K,iBAAiB,CACtCzL,MAAM,CAACiB,KAAK,CAACoK,OAAO,CAACI,iBAAiB,CAAC,CAACC,OAAO,CAAC,CAAC,CAAG,MAAO,CAC7DH,MAAM,cAAErL,IAAA,CAACf,mBAAmB,GAAE,CAAE,CAChCqM,UAAU,CAAE,CAAExE,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAGN5G,KAAA,CAAC7B,GAAG,EAACyM,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACtI,KAAK,CAAE,CAAEuI,YAAY,CAAE,EAAG,CAAE,CAAApI,QAAA,eAEjD3C,IAAA,CAAC1B,GAAG,EAAC0M,EAAE,CAAE,EAAG,CAACS,EAAE,CAAE,EAAG,CAAA9I,QAAA,cAClB3C,IAAA,CAACzB,IAAI,EAACoK,KAAK,CAAC,0BAAM,CAAC+C,KAAK,cAAE1L,IAAA,CAACZ,gBAAgB,GAAE,CAAE,CAAAuD,QAAA,CAC5CY,eAAe,CAACmE,MAAM,CAAG,CAAC,cACzB1H,IAAA,CAACR,GAAG,CAAAmM,aAAA,CAAAA,aAAA,IAAKhE,iBAAiB,MAAEzB,MAAM,CAAE,GAAI,EAAE,CAAC,cAE3ChG,KAAA,QAAKsC,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnD3C,IAAA,CAACnB,IAAI,GAAE,CAAC,cACRmB,IAAA,QAAKwC,KAAK,CAAE,CAAEK,SAAS,CAAE,EAAG,CAAE,CAAAF,QAAA,CAAC,uBAAM,CAAK,CAAC,EACxC,CACN,CACG,CAAC,CACJ,CAAC,cAGN3C,IAAA,CAAC1B,GAAG,EAAC0M,EAAE,CAAE,EAAG,CAACS,EAAE,CAAE,EAAG,CAAA9I,QAAA,cAClB3C,IAAA,CAACzB,IAAI,EAACoK,KAAK,CAAC,4CAAS,CAAC+C,KAAK,cAAE1L,IAAA,CAACb,YAAY,GAAE,CAAE,CAAAwD,QAAA,CAC3CxB,aAAa,EAAIA,aAAa,CAACkD,gBAAgB,cAC9CrE,IAAA,CAACL,YAAY,EACXiM,MAAM,CAAEzG,eAAgB,CACxB3C,KAAK,CAAE,CAAE0D,MAAM,CAAE,OAAQ,CAAE,CAC3B2F,IAAI,CAAE,CAAEC,QAAQ,CAAE,QAAS,CAAE,CAC7BC,QAAQ,CAAE,IAAK,CACfC,UAAU,CAAE,IAAK,CAClB,CAAC,cAEF9L,KAAA,QAAKsC,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnD3C,IAAA,CAACnB,IAAI,GAAE,CAAC,cACRmB,IAAA,QAAKwC,KAAK,CAAE,CAAEK,SAAS,CAAE,EAAG,CAAE,CAAAF,QAAA,CAAC,uBAAM,CAAK,CAAC,EACxC,CACN,CACG,CAAC,CACJ,CAAC,EACH,CAAC,cAGN3C,IAAA,CAAC3B,GAAG,EAACyM,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACtI,KAAK,CAAE,CAAEuI,YAAY,CAAE,EAAG,CAAE,CAAApI,QAAA,cACjD3C,IAAA,CAAC1B,GAAG,EAAC2N,IAAI,CAAE,EAAG,CAAAtJ,QAAA,cACZ3C,IAAA,CAACzB,IAAI,EAACoK,KAAK,CAAC,sCAAQ,CAAC+C,KAAK,cAAE1L,IAAA,CAACV,iBAAiB,GAAE,CAAE,CAAAqD,QAAA,CAC/C4B,iBAAiB,CAACmD,MAAM,CAAG,CAAC,cAC3B1H,IAAA,CAACN,IAAI,CAAAiM,aAAA,CAAAA,aAAA,IAAKhC,cAAc,MAAEzD,MAAM,CAAE,GAAI,EAAE,CAAC,cAEzChG,KAAA,QAAKsC,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnD3C,IAAA,CAACnB,IAAI,GAAE,CAAC,cACRmB,IAAA,QAAKwC,KAAK,CAAE,CAAEK,SAAS,CAAE,EAAG,CAAE,CAAAF,QAAA,CAAC,uBAAM,CAAK,CAAC,EACxC,CACN,CACG,CAAC,CACJ,CAAC,CACH,CAAC,cAGN3C,IAAA,CAAC3B,GAAG,EAACyM,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACtI,KAAK,CAAE,CAAEuI,YAAY,CAAE,EAAG,CAAE,CAAApI,QAAA,cACjD3C,IAAA,CAAC1B,GAAG,EAAC2N,IAAI,CAAE,EAAG,CAAAtJ,QAAA,cACZ3C,IAAA,CAACzB,IAAI,EAACoK,KAAK,CAAC,4CAAS,CAAC+C,KAAK,cAAE1L,IAAA,CAACX,gBAAgB,GAAE,CAAE,CAAAsD,QAAA,CAC/CoB,gBAAgB,CAAC2D,MAAM,CAAG,CAAC,cAC1B1H,IAAA,CAACT,MAAM,CAAAoM,aAAA,CAAAA,aAAA,IAAKzC,kBAAkB,MAAEhD,MAAM,CAAE,GAAI,EAAE,CAAC,cAE/ChG,KAAA,QAAKsC,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnD3C,IAAA,CAACnB,IAAI,GAAE,CAAC,cACRmB,IAAA,QAAKwC,KAAK,CAAE,CAAEK,SAAS,CAAE,EAAG,CAAE,CAAAF,QAAA,CAAC,uBAAM,CAAK,CAAC,EACxC,CACN,CACG,CAAC,CACJ,CAAC,CACH,CAAC,cAENzC,KAAA,CAAC7B,GAAG,EAACyM,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAnI,QAAA,eAEpB3C,IAAA,CAAC1B,GAAG,EAAC0M,EAAE,CAAE,EAAG,CAACS,EAAE,CAAE,EAAG,CAAA9I,QAAA,cAClB3C,IAAA,CAACzB,IAAI,EACHoK,KAAK,CAAC,0BAAM,CACZ+C,KAAK,cAAE1L,IAAA,MAAGkM,IAAI,CAAC,OAAO,CAAAvJ,QAAA,CAAC,0BAAI,CAAG,CAAE,CAChCH,KAAK,CAAE,CAAE0D,MAAM,CAAE,OAAQ,CAAE,CAC3BiG,SAAS,CAAE,CAAEjG,MAAM,CAAE,OAAO,CAAE2C,QAAQ,CAAE,MAAO,CAAE,CAAAlG,QAAA,cAEjD3C,IAAA,CAACvB,IAAI,EACH2N,UAAU,CAAEnL,UAAW,CACvBoL,UAAU,CAAG3I,IAAI,eACf1D,IAAA,CAACvB,IAAI,CAAC6N,IAAI,EAAC9J,KAAK,CAAE,CAAEE,OAAO,CAAE,OAAQ,CAAE,CAAAC,QAAA,cACrC3C,IAAA,CAACvB,IAAI,CAAC6N,IAAI,CAACC,IAAI,EACb5D,KAAK,cACH3I,IAAA,MAAGkM,IAAI,UAAAvH,MAAA,CAAWjB,IAAI,CAACC,GAAG,CAAG,CAACnB,KAAK,CAAE,CAAEiF,QAAQ,CAAE,EAAG,CAAE,CAAA9E,QAAA,CACnDe,IAAI,CAACiF,KAAK,CAACjB,MAAM,CAAG,EAAE,IAAA/C,MAAA,CAAMjB,IAAI,CAACiF,KAAK,CAAC6D,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,QAAQ9I,IAAI,CAACiF,KAAK,CACzE,CACJ,CACD5F,WAAW,cACT7C,KAAA,CAACtB,KAAK,EAAA+D,QAAA,eACJ3C,IAAA,CAACrB,GAAG,EAACmI,KAAK,CAAE1D,gBAAgB,CAACM,IAAI,CAACL,QAAQ,CAAE,CAACT,IAAI,CAAC,OAAO,CAAAD,QAAA,CACtDe,IAAI,CAACL,QAAQ,CACX,CAAC,cACNrD,IAAA,CAACI,IAAI,EAAC4C,IAAI,CAAC,WAAW,CAACR,KAAK,CAAE,CAAEiF,QAAQ,CAAE,EAAG,CAAE,CAAA9E,QAAA,CAC5Ce,IAAI,CAACS,UAAU,CACZ,CAAC,cACPnE,IAAA,CAACI,IAAI,EAAC4C,IAAI,CAAC,WAAW,CAACR,KAAK,CAAE,CAAEiF,QAAQ,CAAE,EAAG,CAAE,CAAA9E,QAAA,CAC5C7C,MAAM,CAAC4D,IAAI,CAAC+I,WAAW,CAAC,CAACjB,OAAO,CAAC,CAAC,CAC/B,CAAC,EACF,CACR,CACF,CAAC,CACO,CACX,CACH,CAAC,CACE,CAAC,CACJ,CAAC,cAGNxL,IAAA,CAAC1B,GAAG,EAAC0M,EAAE,CAAE,EAAG,CAACS,EAAE,CAAE,EAAG,CAAA9I,QAAA,cAClB3C,IAAA,CAACzB,IAAI,EACHoK,KAAK,CAAC,0BAAM,CACZ+C,KAAK,cAAE1L,IAAA,CAACb,YAAY,GAAE,CAAE,CACxBqD,KAAK,CAAE,CAAE0D,MAAM,CAAE,OAAQ,CAAE,CAC3BiG,SAAS,CAAE,CAAEjG,MAAM,CAAE,OAAO,CAAE2C,QAAQ,CAAE,MAAO,CAAE,CAAAlG,QAAA,cAEjD3C,IAAA,CAACvB,IAAI,EACH2N,UAAU,CAAE,CAAAjL,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEuL,kBAAkB,GAAI,EAAG,CACpDL,UAAU,CAAG3I,IAAI,eACfxD,KAAA,CAACzB,IAAI,CAAC6N,IAAI,EAAC9J,KAAK,CAAE,CAAEE,OAAO,CAAE,OAAQ,CAAE,CAAAC,QAAA,eACrC3C,IAAA,CAACvB,IAAI,CAAC6N,IAAI,CAACC,IAAI,EACb5D,KAAK,CAAEjF,IAAI,CAACC,GAAI,CAChBZ,WAAW,IAAA4B,MAAA,CAAKjB,IAAI,CAACG,KAAK,uBAAO,CAClC,CAAC,cACF7D,IAAA,QAAA2C,QAAA,cACE3C,IAAA,CAACrB,GAAG,EAACmI,KAAK,CAAE1D,gBAAgB,CAACM,IAAI,CAACC,GAAG,CAAE,CAAAhB,QAAA,CACpCe,IAAI,CAACG,KAAK,CACR,CAAC,CACH,CAAC,EACG,CACX,CACH,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,cAEN3D,KAAA,CAAC7B,GAAG,EAACyM,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACtI,KAAK,CAAE,CAAEK,SAAS,CAAE,EAAG,CAAE,CAAAF,QAAA,eAE9C3C,IAAA,CAAC1B,GAAG,EAAC0M,EAAE,CAAE,EAAG,CAACS,EAAE,CAAE,EAAG,CAAA9I,QAAA,cAClB3C,IAAA,CAACzB,IAAI,EACHoK,KAAK,CAAC,gCAAO,CACbnG,KAAK,CAAE,CAAE0D,MAAM,CAAE,OAAQ,CAAE,CAC3BiG,SAAS,CAAE,CAAEjG,MAAM,CAAE,OAAO,CAAE2C,QAAQ,CAAE,MAAO,CAAE,CAAAlG,QAAA,cAEjD3C,IAAA,CAACvB,IAAI,EACH2N,UAAU,CAAE,CAAAjL,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEwL,eAAe,GAAI,EAAG,CACjDN,UAAU,CAAG3I,IAAI,eACfxD,KAAA,CAACzB,IAAI,CAAC6N,IAAI,EAAC9J,KAAK,CAAE,CAAEE,OAAO,CAAE,OAAQ,CAAE,CAAAC,QAAA,eACrC3C,IAAA,CAACvB,IAAI,CAAC6N,IAAI,CAACC,IAAI,EACb5D,KAAK,CAAEjF,IAAI,CAACC,GAAG,CAACQ,UAAW,CAC3BpB,WAAW,IAAA4B,MAAA,CAAKjB,IAAI,CAACG,KAAK,uBAAO,CAClC,CAAC,cACF7D,IAAA,QAAA2C,QAAA,cACE3C,IAAA,CAACrB,GAAG,EAACmI,KAAK,CAAC,MAAM,CAAAnE,QAAA,CAAEe,IAAI,CAACG,KAAK,CAAM,CAAC,CACjC,CAAC,EACG,CACX,CACH,CAAC,CACE,CAAC,CACJ,CAAC,cAGN7D,IAAA,CAAC1B,GAAG,EAAC0M,EAAE,CAAE,EAAG,CAACS,EAAE,CAAE,EAAG,CAAA9I,QAAA,cAClBzC,KAAA,CAAC3B,IAAI,EACHoK,KAAK,CAAC,wDAAW,CACjBnG,KAAK,CAAE,CAAE0D,MAAM,CAAE,OAAQ,CAAE,CAC3BiG,SAAS,CAAE,CAAEjG,MAAM,CAAE,OAAO,CAAE2C,QAAQ,CAAE,MAAO,CAAE,CACjD6C,KAAK,CACH,CAAAnK,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEqL,QAAQ,gBACtB1M,KAAA,CAACE,IAAI,EAAC4C,IAAI,CAAC,WAAW,CAAAL,QAAA,EAAC,4BACf,CAACpB,cAAc,CAACqL,QAAQ,CAACC,UAAU,CAAC,KAC5C,EAAM,CAET,CAAAlK,QAAA,EAEA,CAAApB,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEqL,QAAQ,gBACvB1M,KAAA,QAAKsC,KAAK,CAAE,CAAEuI,YAAY,CAAE,EAAE,CAAErI,OAAO,CAAE,EAAE,CAAEoK,UAAU,CAAE,SAAS,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAApK,QAAA,eACpF3C,IAAA,CAACI,IAAI,EAAC4M,MAAM,MAAArK,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxBzC,KAAA,QAAKsC,KAAK,CAAE,CAAEK,SAAS,CAAE,CAAE,CAAE,CAAAF,QAAA,eAC3BzC,KAAA,CAACE,IAAI,EAAAuC,QAAA,EAAC,wCAAQ,CAACpB,cAAc,CAACqL,QAAQ,CAACC,UAAU,CAAC,KAAG,EAAM,CAAC,cAC5D3M,KAAA,QAAKsC,KAAK,CAAE,CAAEK,SAAS,CAAE,CAAE,CAAE,CAAAF,QAAA,eAC3BzC,KAAA,CAACvB,GAAG,EAACmI,KAAK,CAAC,OAAO,CAAAnE,QAAA,EAAC,eAAG,CAACpB,cAAc,CAACqL,QAAQ,CAACK,kBAAkB,CAAC,GAAC,EAAK,CAAC,cACzE/M,KAAA,CAACvB,GAAG,EAACmI,KAAK,CAAC,SAAS,CAAAnE,QAAA,EAAC,eAAG,CAACpB,cAAc,CAACqL,QAAQ,CAACM,iBAAiB,CAAC,GAAC,EAAK,CAAC,cAC1EhN,KAAA,CAACvB,GAAG,EAACmI,KAAK,CAAC,KAAK,CAAAnE,QAAA,EAAC,eAAG,CAACpB,cAAc,CAACqL,QAAQ,CAACO,kBAAkB,CAAC,GAAC,EAAK,CAAC,EACpE,CAAC,EACH,CAAC,EACH,CACN,cACDnN,IAAA,CAACvB,IAAI,EACH2N,UAAU,CAAE,CAAA7K,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE6L,iBAAiB,IAAIjM,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEkD,gBAAgB,GAAI,EAAG,CACvFgI,UAAU,CAAG3I,IAAI,OAAA2J,eAAA,oBACfnN,KAAA,CAACzB,IAAI,CAAC6N,IAAI,EAAC9J,KAAK,CAAE,CAAEE,OAAO,CAAE,OAAQ,CAAE,CAAAC,QAAA,eACrC3C,IAAA,CAACvB,IAAI,CAAC6N,IAAI,CAACC,IAAI,EACb5D,KAAK,cACHzI,KAAA,CAACtB,KAAK,EAAA+D,QAAA,EACHe,IAAI,CAAC4J,OAAO,EAAI5J,IAAI,CAACC,GAAG,CACxB,EAAA0J,eAAA,CAAA3J,IAAI,CAAC6J,SAAS,UAAAF,eAAA,iBAAdA,eAAA,CAAgBG,KAAK,gBAAIxN,IAAA,SAAA2C,QAAA,CAAOe,IAAI,CAAC6J,SAAS,CAACC,KAAK,CAAO,CAAC,EACxD,CACR,CACDzK,WAAW,cACT7C,KAAA,CAACtB,KAAK,EAAA+D,QAAA,eACJzC,KAAA,CAACE,IAAI,EAAC4C,IAAI,CAAC,WAAW,CAAAL,QAAA,EAAC,eAAG,CAACe,IAAI,CAACG,KAAK,CAAC,SAAE,EAAM,CAAC,CAC9CH,IAAI,CAAC6J,SAAS,eACbrN,KAAA,CAACvB,GAAG,EAACmI,KAAK,CACRpD,IAAI,CAAC6J,SAAS,CAACA,SAAS,GAAK,UAAU,CAAG,OAAO,CACjD7J,IAAI,CAAC6J,SAAS,CAACA,SAAS,GAAK,UAAU,CAAG,KAAK,CAAG,SACnD,CAAA5K,QAAA,EACEe,IAAI,CAAC6J,SAAS,CAACA,SAAS,GAAK,UAAU,CAAG,IAAI,CAC9C7J,IAAI,CAAC6J,SAAS,CAACA,SAAS,GAAK,UAAU,CAAG,IAAI,CAAG,IAAI,CACrD7J,IAAI,CAAC6J,SAAS,CAACE,KAAK,CAAC,QACxB,EAAK,CACN,EACI,CACR,CACF,CAAC,cACFzN,IAAA,QAAA2C,QAAA,cACE3C,IAAA,CAACrB,GAAG,EAACmI,KAAK,CAAC,MAAM,CAAAnE,QAAA,CAAEe,IAAI,CAACG,KAAK,CAAM,CAAC,CACjC,CAAC,EACG,CAAC,EACZ,CACH,CAAC,EACE,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}