import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, List, Typography, Tag, Space, Spin, Alert } from 'antd';
import {
  FileTextOutlined,
  DatabaseOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  RiseOutlined,
  PieChartOutlined,
  Bar<PERSON><PERSON>Outlined,
  LineChartOutlined
} from '@ant-design/icons';
import { Column, Pie, Line, Area } from '@ant-design/charts';
import ReactECharts from 'echarts-for-react';
import * as echarts from 'echarts';
import 'echarts-wordcloud';
import axios from 'axios';
import moment from 'moment';

const { Title, Text } = Typography;

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [recentNews, setRecentNews] = useState([]);
  const [trendingStats, setTrendingStats] = useState(null);
  const [timelineStats, setTimelineStats] = useState([]);
  const [sentimentStats, setSentimentStats] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [statsRes, newsRes, trendingRes, timelineRes, sentimentRes] = await Promise.all([
        axios.get('/api/stats'),
        axios.get('/api/news?limit=10'),
        axios.get('/api/stats/trending?period=day&limit=5'),
        axios.get('/api/stats/timeline?period=day&days=7'),
        axios.get('/api/stats/sentiment?period=day&days=7&limit=50')
      ]);

      setStats(statsRes.data.data);
      setRecentNews(newsRes.data.data.items);
      setTrendingStats(trendingRes.data.data);
      setTimelineStats(timelineRes.data.data.timeline);
      setSentimentStats(sentimentRes.data.data);
    } catch (error) {
      console.error('获取仪表板数据失败:', error);
      setError('获取数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="错误"
        description={error}
        type="error"
        showIcon
        action={
          <button onClick={fetchDashboardData}>重试</button>
        }
      />
    );
  }

  const getCategoryColor = (category) => {
    const colors = {
      '技术社区': 'blue',
      '问答社区': 'green',
      '社交媒体': 'orange',
      '新闻媒体': 'red',
      '财经媒体': 'purple',
      '科技媒体': 'cyan',
      '视频平台': 'magenta',
      '短视频': 'lime',
      '投资社区': 'gold'
    };
    return colors[category] || 'default';
  };

  // 准备分类饼图数据
  const categoryPieData = stats?.byCategory?.map(item => ({
    type: item._id,
    value: item.count,
    name: item._id
  })) || [];

  // 准备数据源柱状图数据
  const sourceColumnData = stats?.bySource?.slice(0, 10).map(item => ({
    source: item._id.sourceName,
    count: item.count
  })) || [];

  // 准备词云数据
  const wordCloudData = trendingStats?.trendingKeywords?.map(item => ({
    name: item._id,
    value: item.count
  })) || [];

  // 调试输出
  console.log('词云数据:', wordCloudData);
  console.log('热门统计数据:', trendingStats);

  // 准备时间线图表数据
  const timelineChartData = timelineStats.map(item => {
    const { _id } = item;
    let timeLabel;
    if (_id.hour !== undefined) {
      timeLabel = `${_id.month.toString().padStart(2, '0')}-${_id.day.toString().padStart(2, '0')} ${_id.hour}:00`;
    } else if (_id.day !== undefined) {
      timeLabel = `${_id.month.toString().padStart(2, '0')}-${_id.day.toString().padStart(2, '0')}`;
    } else if (_id.week !== undefined) {
      timeLabel = `第${_id.week}周`;
    } else {
      timeLabel = `${_id.year}-${_id.month.toString().padStart(2, '0')}`;
    }
    return {
      time: timeLabel,
      count: item.count
    };
  });

  // 词云配置
  const wordCloudOption = {
    backgroundColor: 'transparent',
    tooltip: {
      show: true,
      formatter: function (params) {
        return `${params.name} (${params.value})`;
      }
    },
    series: [{
      type: 'wordCloud',
      // 高级布局优化参数
      gridSize: 12,  // 进一步增加网格大小，确保词语不重叠
      sizeRange: [18, 45],  // 调整字体大小范围，减小最大字体避免过度拥挤
      rotationRange: [0, 0],  // 保持水平排列，避免旋转造成的混乱
      rotationStep: 0,
      shape: 'circle',  // 使用圆形布局，更加紧凑
      left: 'center',
      top: 'center',
      width: '85%',  // 进一步缩小显示区域，增加边距
      height: '85%',
      right: null,
      bottom: null,
      drawOutOfBound: false,
      layoutAnimation: true,
      // 布局算法优化
      maskImage: null,
      keepAspect: true,  // 保持宽高比，避免变形
      // 词语间距优化
      padding: 8,  // 增加词语间距
      // 布局尝试次数，提高布局质量
      layoutAnimationDuration: 1000,
      layoutAnimationEasing: 'cubicOut',
      textStyle: {
        fontFamily: 'Microsoft YaHei, PingFang SC, Helvetica Neue, Arial, sans-serif',
        fontWeight: '600',  // 使用半粗体，平衡清晰度和美观
        color: function (params) {
          // 根据词频大小使用渐变色系
          const value = params.value;
          const maxValue = Math.max(...(wordCloudData || []).map(item => item.value));
          const ratio = value / maxValue;

          // 使用蓝色系渐变，高频词颜色更深
          if (ratio > 0.8) {
            return '#1890ff';  // 深蓝
          } else if (ratio > 0.6) {
            return '#40a9ff';  // 中蓝
          } else if (ratio > 0.4) {
            return '#69c0ff';  // 浅蓝
          } else if (ratio > 0.2) {
            return '#91d5ff';  // 很浅蓝
          } else {
            return '#bae7ff';  // 极浅蓝
          }
        }
      },
      emphasis: {
        focus: 'self',
        textStyle: {
          shadowBlur: 6,
          shadowColor: 'rgba(24, 144, 255, 0.4)',
          shadowOffsetX: 1,
          shadowOffsetY: 1,
          fontSize: function(params) {
            // hover时字体稍微放大
            return params.fontSize * 1.1;
          }
        }
      },
      data: wordCloudData && wordCloudData.length > 0 ? wordCloudData : [
        { name: '暂无数据', value: 1 }
      ]
    }]
  };

  // 分类饼图配置
  const categoryPieConfig = {
    appendPadding: 10,
    data: categoryPieData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.75,
    innerRadius: 0.3,
    label: {
      type: 'inner',
      offset: '-30%',
      content: function(data) {
        return data.value > 20 ? `${data.type}\n${data.value}` : '';
      },
      style: {
        fontSize: 11,
        textAlign: 'center',
        fill: 'white',
        fontWeight: 'bold'
      }
    },
    legend: {
      position: 'right',
      offsetX: -20,
      itemName: {
        style: {
          fontSize: 12
        }
      }
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.type, value: `${datum.value} 条` };
      }
    },
    statistic: {
      title: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: '总计',
      },
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: categoryPieData.reduce((sum, item) => sum + item.value, 0).toString(),
      },
    },
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-active',
      },
    ],
  };

  // 数据源柱状图配置
  const sourceColumnConfig = {
    data: sourceColumnData,
    xField: 'source',
    yField: 'count',
    label: {
      position: 'middle',
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    meta: {
      source: {
        alias: '数据源',
      },
      count: {
        alias: '新闻数量',
      },
    },
  };

  // 时间线图表配置
  const timelineConfig = {
    data: timelineChartData,
    xField: 'time',
    yField: 'count',
    smooth: true,
    padding: [20, 20, 50, 60], // 增加左侧padding确保Y轴显示
    point: {
      size: 4,
      shape: 'circle',
      style: {
        fill: 'white',
        stroke: '#1890ff',
        lineWidth: 2,
      },
    },
    line: {
      style: {
        stroke: '#1890ff',
        lineWidth: 3,
      },
    },
    area: {
      style: {
        fill: 'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff',
        fillOpacity: 0.3,
      },
    },
    xAxis: {
      position: 'bottom',
      label: {
        autoRotate: true,
        style: {
          fontSize: 12,
        },
      },
      title: {
        text: '时间',
        position: 'end',
        style: {
          fontSize: 14,
          fontWeight: 'bold',
        },
      },
      grid: {
        line: {
          style: {
            stroke: '#f0f0f0',
            lineWidth: 1,
            lineDash: [4, 5],
          },
        },
      },
    },
    yAxis: {
      position: 'left',
      min: 0,
      nice: true,
      label: {
        style: {
          fontSize: 12,
        },
      },
      title: {
        text: '更新数量',
        position: 'end',
        style: {
          fontSize: 14,
          fontWeight: 'bold',
        },
      },
      grid: {
        line: {
          style: {
            stroke: '#f0f0f0',
            lineWidth: 1,
            lineDash: [4, 5],
          },
        },
      },
    },
    tooltip: {
      showMarkers: true,
      formatter: (datum) => {
        return { name: '更新数量', value: `${datum.count} 条` };
      },
    },
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  return (
    <div>
      <Title level={2}>仪表板</Title>
      
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总新闻数量"
              value={stats?.general?.totalCount || 0}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="数据源数量"
              value={stats?.bySource?.length || 0}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="分类数量"
              value={stats?.byCategory?.length || 0}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="最新更新"
              value={stats?.general?.latestCollectTime ? 
                moment(stats.general.latestCollectTime).fromNow() : '暂无数据'}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表展示区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {/* 分类分布饼图 */}
        <Col xs={24} lg={12}>
          <Card title="分类分布" extra={<PieChartOutlined />}>
            {categoryPieData.length > 0 ? (
              <Pie {...categoryPieConfig} height={300} />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin />
                <div style={{ marginTop: 16 }}>加载中...</div>
              </div>
            )}
          </Card>
        </Col>

        {/* 热门关键词词云 */}
        <Col xs={24} lg={12}>
          <Card title="热门关键词词云" extra={<RiseOutlined />}>
            {trendingStats && trendingStats.trendingKeywords ? (
              <ReactECharts
                option={wordCloudOption}
                style={{ height: '300px' }}
                opts={{ renderer: 'canvas' }}
                notMerge={true}
                lazyUpdate={true}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin />
                <div style={{ marginTop: 16 }}>加载中...</div>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 时间线趋势图 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="数据更新趋势" extra={<LineChartOutlined />}>
            {timelineChartData.length > 0 ? (
              <Area {...timelineConfig} height={300} />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin />
                <div style={{ marginTop: 16 }}>加载中...</div>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 数据源统计柱状图 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="热门数据源统计" extra={<BarChartOutlined />}>
            {sourceColumnData.length > 0 ? (
              <Column {...sourceColumnConfig} height={300} />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin />
                <div style={{ marginTop: 16 }}>加载中...</div>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最新新闻 */}
        <Col xs={24} lg={12}>
          <Card
            title="最新新闻"
            extra={<a href="/news">查看更多</a>}
            style={{ height: '400px' }}
            bodyStyle={{ height: '340px', overflow: 'auto' }}
          >
            <List
              dataSource={recentNews}
              renderItem={(item) => (
                <List.Item style={{ padding: '8px 0' }}>
                  <List.Item.Meta
                    title={
                      <a href={`/news/${item._id}`} style={{ fontSize: 14 }}>
                        {item.title.length > 50 ? `${item.title.substring(0, 50)}...` : item.title}
                      </a>
                    }
                    description={
                      <Space>
                        <Tag color={getCategoryColor(item.category)} size="small">
                          {item.category}
                        </Tag>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {item.sourceName}
                        </Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {moment(item.collectTime).fromNow()}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 热门分类列表 */}
        <Col xs={24} lg={12}>
          <Card
            title="热门分类"
            extra={<RiseOutlined />}
            style={{ height: '400px' }}
            bodyStyle={{ height: '340px', overflow: 'auto' }}
          >
            <List
              dataSource={trendingStats?.trendingCategories || []}
              renderItem={(item) => (
                <List.Item style={{ padding: '8px 0' }}>
                  <List.Item.Meta
                    title={item._id}
                    description={`${item.count} 条新闻`}
                  />
                  <div>
                    <Tag color={getCategoryColor(item._id)}>
                      {item.count}
                    </Tag>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 热门数据源 */}
        <Col xs={24} lg={12}>
          <Card
            title="热门数据源"
            style={{ height: '400px' }}
            bodyStyle={{ height: '340px', overflow: 'auto' }}
          >
            <List
              dataSource={trendingStats?.trendingSources || []}
              renderItem={(item) => (
                <List.Item style={{ padding: '8px 0' }}>
                  <List.Item.Meta
                    title={item._id.sourceName}
                    description={`${item.count} 条新闻`}
                  />
                  <div>
                    <Tag color="blue">{item.count}</Tag>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 热门关键词（带情感分析） */}
        <Col xs={24} lg={12}>
          <Card
            title="热门关键词情感分析"
            style={{ height: '400px' }}
            bodyStyle={{ height: '340px', overflow: 'auto' }}
            extra={
              sentimentStats?.overview && (
                <Text type="secondary">
                  总体情感: {sentimentStats.overview.totalScore}/10
                </Text>
              )
            }
          >
            {sentimentStats?.overview && (
              <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>
                <Text strong>情感概览</Text>
                <div style={{ marginTop: 8 }}>
                  <Text>总体情感指数: {sentimentStats.overview.totalScore}/10</Text>
                  <div style={{ marginTop: 4 }}>
                    <Tag color="green">正面 {sentimentStats.overview.positivePercentage}%</Tag>
                    <Tag color="default">中性 {sentimentStats.overview.neutralPercentage}%</Tag>
                    <Tag color="red">负面 {sentimentStats.overview.negativePercentage}%</Tag>
                  </div>
                </div>
              </div>
            )}
            <List
              dataSource={sentimentStats?.keywordSentiments || trendingStats?.trendingKeywords || []}
              renderItem={(item) => (
                <List.Item style={{ padding: '8px 0' }}>
                  <List.Item.Meta
                    title={
                      <Space>
                        {item.keyword || item._id}
                        {item.sentiment?.emoji && <span>{item.sentiment.emoji}</span>}
                      </Space>
                    }
                    description={
                      <Space>
                        <Text type="secondary">出现 {item.count} 次</Text>
                        {item.sentiment && (
                          <Tag color={
                            item.sentiment.sentiment === 'positive' ? 'green' :
                            item.sentiment.sentiment === 'negative' ? 'red' : 'default'
                          }>
                            {item.sentiment.sentiment === 'positive' ? '正面' :
                             item.sentiment.sentiment === 'negative' ? '负面' : '中性'}
                            {item.sentiment.score}分
                          </Tag>
                        )}
                      </Space>
                    }
                  />
                  <div>
                    <Tag color="blue">{item.count}</Tag>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
