[{"D:\\舆情监控\\client\\src\\index.js": "1", "D:\\舆情监控\\client\\src\\App.js": "2", "D:\\舆情监控\\client\\src\\reportWebVitals.js": "3", "D:\\舆情监控\\client\\src\\components\\Layout.js": "4", "D:\\舆情监控\\client\\src\\pages\\Dashboard.js": "5", "D:\\舆情监控\\client\\src\\pages\\NewsList.js": "6", "D:\\舆情监控\\client\\src\\pages\\NewsDetail.js": "7", "D:\\舆情监控\\client\\src\\pages\\Statistics.js": "8", "D:\\舆情监控\\client\\src\\pages\\SystemManagement.js": "9", "D:\\舆情监控\\client\\src\\contexts\\ThemeContext.js": "10", "D:\\舆情监控\\client\\src\\components\\ThemeToggle.js": "11"}, {"size": 535, "mtime": 1754729899697, "results": "12", "hashOfConfig": "13"}, {"size": 1126, "mtime": 1754737778611, "results": "14", "hashOfConfig": "13"}, {"size": 362, "mtime": 1754729899923, "results": "15", "hashOfConfig": "13"}, {"size": 3164, "mtime": 1754737839840, "results": "16", "hashOfConfig": "13"}, {"size": 20058, "mtime": 1754743568021, "results": "17", "hashOfConfig": "13"}, {"size": 9240, "mtime": 1754736701824, "results": "18", "hashOfConfig": "13"}, {"size": 8712, "mtime": 1754730119311, "results": "19", "hashOfConfig": "13"}, {"size": 16669, "mtime": 1754740117656, "results": "20", "hashOfConfig": "13"}, {"size": 11562, "mtime": 1754730214286, "results": "21", "hashOfConfig": "13"}, {"size": 2214, "mtime": 1754737720017, "results": "22", "hashOfConfig": "13"}, {"size": 765, "mtime": 1754737733312, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "p9i734", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\舆情监控\\client\\src\\index.js", [], [], "D:\\舆情监控\\client\\src\\App.js", [], [], "D:\\舆情监控\\client\\src\\reportWebVitals.js", [], [], "D:\\舆情监控\\client\\src\\components\\Layout.js", [], [], "D:\\舆情监控\\client\\src\\pages\\Dashboard.js", ["57", "58"], [], "D:\\舆情监控\\client\\src\\pages\\NewsList.js", ["59", "60", "61", "62"], [], "D:\\舆情监控\\client\\src\\pages\\NewsDetail.js", ["63", "64"], [], "D:\\舆情监控\\client\\src\\pages\\Statistics.js", ["65", "66"], [], "D:\\舆情监控\\client\\src\\pages\\SystemManagement.js", ["67", "68"], [], "D:\\舆情监控\\client\\src\\contexts\\ThemeContext.js", [], [], "D:\\舆情监控\\client\\src\\components\\ThemeToggle.js", [], [], {"ruleId": "69", "severity": 1, "message": "70", "line": 13, "column": 23, "nodeType": "71", "messageId": "72", "endLine": 13, "endColumn": 27}, {"ruleId": "69", "severity": 1, "message": "73", "line": 15, "column": 13, "nodeType": "71", "messageId": "72", "endLine": 15, "endColumn": 20}, {"ruleId": "69", "severity": 1, "message": "74", "line": 14, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 14, "endColumn": 7}, {"ruleId": "69", "severity": 1, "message": "75", "line": 15, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 15, "endColumn": 8}, {"ruleId": "76", "severity": 1, "message": "77", "line": 52, "column": 6, "nodeType": "78", "endLine": 52, "endColumn": 8, "suggestions": "79"}, {"ruleId": "80", "severity": 1, "message": "81", "line": 168, "column": 11, "nodeType": "82", "endLine": 171, "endColumn": 12}, {"ruleId": "69", "severity": 1, "message": "83", "line": 13, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 13, "endColumn": 10}, {"ruleId": "76", "severity": 1, "message": "84", "line": 38, "column": 6, "nodeType": "78", "endLine": 38, "endColumn": 10, "suggestions": "85"}, {"ruleId": "69", "severity": 1, "message": "86", "line": 21, "column": 29, "nodeType": "71", "messageId": "72", "endLine": 21, "endColumn": 33}, {"ruleId": "76", "severity": 1, "message": "87", "line": 45, "column": 6, "nodeType": "78", "endLine": 45, "endColumn": 36, "suggestions": "88"}, {"ruleId": "69", "severity": 1, "message": "89", "line": 17, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 17, "endColumn": 11}, {"ruleId": "69", "severity": 1, "message": "90", "line": 24, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 24, "endColumn": 22}, "no-unused-vars", "'Line' is defined but never used.", "Identifier", "unusedVar", "'echarts' is defined but never used.", "'Spin' is defined but never used.", "'Alert' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchNews'. Either include it or remove the dependency array.", "ArrayExpression", ["91"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'Divider' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchNewsDetail'. Either include it or remove the dependency array.", ["92"], "'Area' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTimelineStats'. Either include it or remove the dependency array.", ["93"], "'Progress' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", {"desc": "94", "fix": "95"}, {"desc": "96", "fix": "97"}, {"desc": "98", "fix": "99"}, "Update the dependencies array to be: [fetchNews]", {"range": "100", "text": "101"}, "Update the dependencies array to be: [fetchNewsDetail, id]", {"range": "102", "text": "103"}, "Update the dependencies array to be: [timelinePeriod, timelineDays, fetchTimelineStats]", {"range": "104", "text": "105"}, [1179, 1181], "[fetchNews]", [799, 803], "[fetchNewsDetail, id]", [1100, 1130], "[timelinePeriod, timelineDays, fetchTimelineStats]"]