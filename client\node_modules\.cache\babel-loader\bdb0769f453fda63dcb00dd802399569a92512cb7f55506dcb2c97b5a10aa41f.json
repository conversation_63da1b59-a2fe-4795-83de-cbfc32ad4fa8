{"ast": null, "code": "var _jsxFileName = \"D:\\\\\\u8206\\u60C5\\u76D1\\u63A7\\\\client\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, List, Typography, Tag, Space, Spin, Alert } from 'antd';\nimport { FileTextOutlined, DatabaseOutlined, ClockCircleOutlined, TrophyOutlined, RiseOutlined, PieChartOutlined, BarChartOutlined, LineChartOutlined } from '@ant-design/icons';\nimport { Column, Pie, Line, Area } from '@ant-design/charts';\nimport ReactECharts from 'echarts-for-react';\nimport * as echarts from 'echarts';\nimport 'echarts-wordcloud';\nimport axios from 'axios';\nimport moment from 'moment';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst Dashboard = () => {\n  _s();\n  var _stats$byCategory, _stats$bySource, _trendingStats$trendi, _stats$general, _stats$bySource2, _stats$byCategory2, _stats$general2;\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState(null);\n  const [recentNews, setRecentNews] = useState([]);\n  const [trendingStats, setTrendingStats] = useState(null);\n  const [timelineStats, setTimelineStats] = useState([]);\n  const [sentimentStats, setSentimentStats] = useState(null);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const [statsRes, newsRes, trendingRes, timelineRes, sentimentRes] = await Promise.all([axios.get('/api/stats'), axios.get('/api/news?limit=10'), axios.get('/api/stats/trending?period=day&limit=5'), axios.get('/api/stats/timeline?period=day&days=7'), axios.get('/api/stats/sentiment?period=day&days=7&limit=50')]);\n      setStats(statsRes.data.data);\n      setRecentNews(newsRes.data.data.items);\n      setTrendingStats(trendingRes.data.data);\n      setTimelineStats(timelineRes.data.data.timeline);\n      setSentimentStats(sentimentRes.data.data);\n    } catch (error) {\n      console.error('获取仪表板数据失败:', error);\n      setError('获取数据失败，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: \"\\u52A0\\u8F7D\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u9519\\u8BEF\",\n      description: error,\n      type: \"error\",\n      showIcon: true,\n      action: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchDashboardData,\n        children: \"\\u91CD\\u8BD5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this);\n  }\n  const getCategoryColor = category => {\n    const colors = {\n      '技术社区': 'blue',\n      '问答社区': 'green',\n      '社交媒体': 'orange',\n      '新闻媒体': 'red',\n      '财经媒体': 'purple',\n      '科技媒体': 'cyan',\n      '视频平台': 'magenta',\n      '短视频': 'lime',\n      '投资社区': 'gold'\n    };\n    return colors[category] || 'default';\n  };\n\n  // 准备分类饼图数据\n  const categoryPieData = (stats === null || stats === void 0 ? void 0 : (_stats$byCategory = stats.byCategory) === null || _stats$byCategory === void 0 ? void 0 : _stats$byCategory.map(item => ({\n    type: item._id,\n    value: item.count,\n    name: item._id\n  }))) || [];\n\n  // 准备数据源柱状图数据\n  const sourceColumnData = (stats === null || stats === void 0 ? void 0 : (_stats$bySource = stats.bySource) === null || _stats$bySource === void 0 ? void 0 : _stats$bySource.slice(0, 10).map(item => ({\n    source: item._id.sourceName,\n    count: item.count\n  }))) || [];\n\n  // 准备词云数据\n  const wordCloudData = (trendingStats === null || trendingStats === void 0 ? void 0 : (_trendingStats$trendi = trendingStats.trendingKeywords) === null || _trendingStats$trendi === void 0 ? void 0 : _trendingStats$trendi.map(item => ({\n    name: item._id,\n    value: item.count\n  }))) || [];\n\n  // 调试输出\n  console.log('词云数据:', wordCloudData);\n  console.log('热门统计数据:', trendingStats);\n\n  // 准备时间线图表数据\n  const timelineChartData = timelineStats.map(item => {\n    const {\n      _id\n    } = item;\n    let timeLabel;\n    if (_id.hour !== undefined) {\n      timeLabel = `${_id.month.toString().padStart(2, '0')}-${_id.day.toString().padStart(2, '0')} ${_id.hour}:00`;\n    } else if (_id.day !== undefined) {\n      timeLabel = `${_id.month.toString().padStart(2, '0')}-${_id.day.toString().padStart(2, '0')}`;\n    } else if (_id.week !== undefined) {\n      timeLabel = `第${_id.week}周`;\n    } else {\n      timeLabel = `${_id.year}-${_id.month.toString().padStart(2, '0')}`;\n    }\n    return {\n      time: timeLabel,\n      count: item.count\n    };\n  });\n\n  // 词云配置\n  const wordCloudOption = {\n    backgroundColor: 'transparent',\n    tooltip: {\n      show: true,\n      formatter: function (params) {\n        return `${params.name}: ${params.value} 次`;\n      }\n    },\n    series: [{\n      type: 'wordCloud',\n      // 优化布局参数\n      gridSize: 8,\n      // 增加网格大小，减少重叠\n      sizeRange: [16, 50],\n      // 调整字体大小范围，使差异更明显\n      rotationRange: [0, 0],\n      // 禁用旋转，保持水平排列\n      rotationStep: 0,\n      shape: 'circle',\n      left: 'center',\n      top: 'center',\n      width: '90%',\n      // 稍微缩小宽度，留出边距\n      height: '90%',\n      // 稍微缩小高度，留出边距\n      right: null,\n      bottom: null,\n      drawOutOfBound: false,\n      layoutAnimation: true,\n      // 优化布局算法\n      maskImage: null,\n      keepAspect: false,\n      // 增加词间距\n      padding: 5,\n      textStyle: {\n        fontFamily: 'Microsoft YaHei, Arial, sans-serif',\n        fontWeight: 'bold',\n        // 使用粗体，更清晰\n        color: function (params) {\n          // 根据词频使用不同颜色深度\n          const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa541c', '#2f54eb', '#a0d911', '#fadb14', '#ff4d4f'];\n          const index = params.dataIndex % colors.length;\n          return colors[index];\n        }\n      },\n      emphasis: {\n        focus: 'self',\n        textStyle: {\n          shadowBlur: 8,\n          shadowColor: 'rgba(0, 0, 0, 0.3)',\n          shadowOffsetX: 2,\n          shadowOffsetY: 2\n        }\n      },\n      data: wordCloudData && wordCloudData.length > 0 ? wordCloudData : [{\n        name: '暂无数据',\n        value: 1\n      }]\n    }]\n  };\n\n  // 分类饼图配置\n  const categoryPieConfig = {\n    appendPadding: 10,\n    data: categoryPieData,\n    angleField: 'value',\n    colorField: 'type',\n    radius: 0.75,\n    innerRadius: 0.3,\n    label: {\n      type: 'inner',\n      offset: '-30%',\n      content: function (data) {\n        return data.value > 20 ? `${data.type}\\n${data.value}` : '';\n      },\n      style: {\n        fontSize: 11,\n        textAlign: 'center',\n        fill: 'white',\n        fontWeight: 'bold'\n      }\n    },\n    legend: {\n      position: 'right',\n      offsetX: -20,\n      itemName: {\n        style: {\n          fontSize: 12\n        }\n      }\n    },\n    tooltip: {\n      formatter: datum => {\n        return {\n          name: datum.type,\n          value: `${datum.value} 条`\n        };\n      }\n    },\n    statistic: {\n      title: {\n        style: {\n          whiteSpace: 'pre-wrap',\n          overflow: 'hidden',\n          textOverflow: 'ellipsis'\n        },\n        content: '总计'\n      },\n      content: {\n        style: {\n          whiteSpace: 'pre-wrap',\n          overflow: 'hidden',\n          textOverflow: 'ellipsis'\n        },\n        content: categoryPieData.reduce((sum, item) => sum + item.value, 0).toString()\n      }\n    },\n    interactions: [{\n      type: 'element-selected'\n    }, {\n      type: 'element-active'\n    }]\n  };\n\n  // 数据源柱状图配置\n  const sourceColumnConfig = {\n    data: sourceColumnData,\n    xField: 'source',\n    yField: 'count',\n    label: {\n      position: 'middle',\n      style: {\n        fill: '#FFFFFF',\n        opacity: 0.6\n      }\n    },\n    xAxis: {\n      label: {\n        autoHide: true,\n        autoRotate: false\n      }\n    },\n    meta: {\n      source: {\n        alias: '数据源'\n      },\n      count: {\n        alias: '新闻数量'\n      }\n    }\n  };\n\n  // 时间线图表配置\n  const timelineConfig = {\n    data: timelineChartData,\n    xField: 'time',\n    yField: 'count',\n    smooth: true,\n    padding: [20, 20, 50, 60],\n    // 增加左侧padding确保Y轴显示\n    point: {\n      size: 4,\n      shape: 'circle',\n      style: {\n        fill: 'white',\n        stroke: '#1890ff',\n        lineWidth: 2\n      }\n    },\n    line: {\n      style: {\n        stroke: '#1890ff',\n        lineWidth: 3\n      }\n    },\n    area: {\n      style: {\n        fill: 'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff',\n        fillOpacity: 0.3\n      }\n    },\n    xAxis: {\n      position: 'bottom',\n      label: {\n        autoRotate: true,\n        style: {\n          fontSize: 12\n        }\n      },\n      title: {\n        text: '时间',\n        position: 'end',\n        style: {\n          fontSize: 14,\n          fontWeight: 'bold'\n        }\n      },\n      grid: {\n        line: {\n          style: {\n            stroke: '#f0f0f0',\n            lineWidth: 1,\n            lineDash: [4, 5]\n          }\n        }\n      }\n    },\n    yAxis: {\n      position: 'left',\n      min: 0,\n      nice: true,\n      label: {\n        style: {\n          fontSize: 12\n        }\n      },\n      title: {\n        text: '更新数量',\n        position: 'end',\n        style: {\n          fontSize: 14,\n          fontWeight: 'bold'\n        }\n      },\n      grid: {\n        line: {\n          style: {\n            stroke: '#f0f0f0',\n            lineWidth: 1,\n            lineDash: [4, 5]\n          }\n        }\n      }\n    },\n    tooltip: {\n      showMarkers: true,\n      formatter: datum => {\n        return {\n          name: '更新数量',\n          value: `${datum.count} 条`\n        };\n      }\n    },\n    animation: {\n      appear: {\n        animation: 'path-in',\n        duration: 1000\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u4EEA\\u8868\\u677F\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u65B0\\u95FB\\u6570\\u91CF\",\n            value: (stats === null || stats === void 0 ? void 0 : (_stats$general = stats.general) === null || _stats$general === void 0 ? void 0 : _stats$general.totalCount) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6570\\u636E\\u6E90\\u6570\\u91CF\",\n            value: (stats === null || stats === void 0 ? void 0 : (_stats$bySource2 = stats.bySource) === null || _stats$bySource2 === void 0 ? void 0 : _stats$bySource2.length) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5206\\u7C7B\\u6570\\u91CF\",\n            value: (stats === null || stats === void 0 ? void 0 : (_stats$byCategory2 = stats.byCategory) === null || _stats$byCategory2 === void 0 ? void 0 : _stats$byCategory2.length) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6700\\u65B0\\u66F4\\u65B0\",\n            value: stats !== null && stats !== void 0 && (_stats$general2 = stats.general) !== null && _stats$general2 !== void 0 && _stats$general2.latestCollectTime ? moment(stats.general.latestCollectTime).fromNow() : '暂无数据',\n            prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5206\\u7C7B\\u5206\\u5E03\",\n          extra: /*#__PURE__*/_jsxDEV(PieChartOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 37\n          }, this),\n          children: categoryPieData.length > 0 ? /*#__PURE__*/_jsxDEV(Pie, {\n            ...categoryPieConfig,\n            height: 300\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '50px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Spin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 16\n              },\n              children: \"\\u52A0\\u8F7D\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u70ED\\u95E8\\u5173\\u952E\\u8BCD\\u8BCD\\u4E91\",\n          extra: /*#__PURE__*/_jsxDEV(RiseOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 40\n          }, this),\n          children: trendingStats && trendingStats.trendingKeywords ? /*#__PURE__*/_jsxDEV(ReactECharts, {\n            option: wordCloudOption,\n            style: {\n              height: '300px'\n            },\n            opts: {\n              renderer: 'canvas'\n            },\n            notMerge: true,\n            lazyUpdate: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '50px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Spin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 16\n              },\n              children: \"\\u52A0\\u8F7D\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6570\\u636E\\u66F4\\u65B0\\u8D8B\\u52BF\",\n          extra: /*#__PURE__*/_jsxDEV(LineChartOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 39\n          }, this),\n          children: timelineChartData.length > 0 ? /*#__PURE__*/_jsxDEV(Area, {\n            ...timelineConfig,\n            height: 300\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '50px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Spin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 16\n              },\n              children: \"\\u52A0\\u8F7D\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u70ED\\u95E8\\u6570\\u636E\\u6E90\\u7EDF\\u8BA1\",\n          extra: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 40\n          }, this),\n          children: sourceColumnData.length > 0 ? /*#__PURE__*/_jsxDEV(Column, {\n            ...sourceColumnConfig,\n            height: 300\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '50px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Spin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 16\n              },\n              children: \"\\u52A0\\u8F7D\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6700\\u65B0\\u65B0\\u95FB\",\n          extra: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/news\",\n            children: \"\\u67E5\\u770B\\u66F4\\u591A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 20\n          }, this),\n          style: {\n            height: '400px'\n          },\n          bodyStyle: {\n            height: '340px',\n            overflow: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: recentNews,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              style: {\n                padding: '8px 0'\n              },\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: `/news/${item._id}`,\n                  style: {\n                    fontSize: 14\n                  },\n                  children: item.title.length > 50 ? `${item.title.substring(0, 50)}...` : item.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Tag, {\n                    color: getCategoryColor(item.category),\n                    size: \"small\",\n                    children: item.category\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 12\n                    },\n                    children: item.sourceName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 12\n                    },\n                    children: moment(item.collectTime).fromNow()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u70ED\\u95E8\\u5206\\u7C7B\",\n          extra: /*#__PURE__*/_jsxDEV(RiseOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 20\n          }, this),\n          style: {\n            height: '400px'\n          },\n          bodyStyle: {\n            height: '340px',\n            overflow: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: (trendingStats === null || trendingStats === void 0 ? void 0 : trendingStats.trendingCategories) || [],\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              style: {\n                padding: '8px 0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: item._id,\n                description: `${item.count} 条新闻`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Tag, {\n                  color: getCategoryColor(item._id),\n                  children: item.count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginTop: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u70ED\\u95E8\\u6570\\u636E\\u6E90\",\n          style: {\n            height: '400px'\n          },\n          bodyStyle: {\n            height: '340px',\n            overflow: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: (trendingStats === null || trendingStats === void 0 ? void 0 : trendingStats.trendingSources) || [],\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              style: {\n                padding: '8px 0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: item._id.sourceName,\n                description: `${item.count} 条新闻`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: item.count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u70ED\\u95E8\\u5173\\u952E\\u8BCD\\u60C5\\u611F\\u5206\\u6790\",\n          style: {\n            height: '400px'\n          },\n          bodyStyle: {\n            height: '340px',\n            overflow: 'auto'\n          },\n          extra: (sentimentStats === null || sentimentStats === void 0 ? void 0 : sentimentStats.overview) && /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u603B\\u4F53\\u60C5\\u611F: \", sentimentStats.overview.totalScore, \"/10\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 17\n          }, this),\n          children: [(sentimentStats === null || sentimentStats === void 0 ? void 0 : sentimentStats.overview) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 16,\n              padding: 12,\n              background: '#f5f5f5',\n              borderRadius: 6\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: \"\\u60C5\\u611F\\u6982\\u89C8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                children: [\"\\u603B\\u4F53\\u60C5\\u611F\\u6307\\u6570: \", sentimentStats.overview.totalScore, \"/10\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"green\",\n                  children: [\"\\u6B63\\u9762 \", sentimentStats.overview.positivePercentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"default\",\n                  children: [\"\\u4E2D\\u6027 \", sentimentStats.overview.neutralPercentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"red\",\n                  children: [\"\\u8D1F\\u9762 \", sentimentStats.overview.negativePercentage, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            dataSource: (sentimentStats === null || sentimentStats === void 0 ? void 0 : sentimentStats.keywordSentiments) || (trendingStats === null || trendingStats === void 0 ? void 0 : trendingStats.trendingKeywords) || [],\n            renderItem: item => {\n              var _item$sentiment;\n              return /*#__PURE__*/_jsxDEV(List.Item, {\n                style: {\n                  padding: '8px 0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                  title: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [item.keyword || item._id, ((_item$sentiment = item.sentiment) === null || _item$sentiment === void 0 ? void 0 : _item$sentiment.emoji) && /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: item.sentiment.emoji\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 631,\n                      columnNumber: 51\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 23\n                  }, this),\n                  description: /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u51FA\\u73B0 \", item.count, \" \\u6B21\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 636,\n                      columnNumber: 25\n                    }, this), item.sentiment && /*#__PURE__*/_jsxDEV(Tag, {\n                      color: item.sentiment.sentiment === 'positive' ? 'green' : item.sentiment.sentiment === 'negative' ? 'red' : 'default',\n                      children: [item.sentiment.sentiment === 'positive' ? '正面' : item.sentiment.sentiment === 'negative' ? '负面' : '中性', item.sentiment.score, \"\\u5206\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 635,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"blue\",\n                    children: item.count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 17\n              }, this);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 387,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"O5daFxUJQ9PnbEuX+k0N9yl8Vtc=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Statistic", "List", "Typography", "Tag", "Space", "Spin", "<PERSON><PERSON>", "FileTextOutlined", "DatabaseOutlined", "ClockCircleOutlined", "TrophyOutlined", "RiseOutlined", "PieChartOutlined", "BarChartOutlined", "LineChartOutlined", "Column", "Pie", "Line", "Area", "ReactECharts", "echarts", "axios", "moment", "jsxDEV", "_jsxDEV", "Title", "Text", "Dashboard", "_s", "_stats$byCategory", "_stats$bySource", "_trendingStats$trendi", "_stats$general", "_stats$bySource2", "_stats$byCategory2", "_stats$general2", "loading", "setLoading", "stats", "setStats", "recentNews", "setRecentNews", "trendingStats", "setTrendingStats", "timelineStats", "setTimelineStats", "sentimentStats", "setSentimentStats", "error", "setError", "fetchDashboardData", "statsRes", "newsRes", "trendingRes", "timelineRes", "sentimentRes", "Promise", "all", "get", "data", "items", "timeline", "console", "style", "textAlign", "padding", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "message", "description", "type", "showIcon", "action", "onClick", "getCategoryColor", "category", "colors", "categoryPieData", "byCategory", "map", "item", "_id", "value", "count", "name", "sourceColumnData", "bySource", "slice", "source", "sourceName", "wordCloudData", "trendingKeywords", "log", "timelineChartData", "time<PERSON><PERSON><PERSON>", "hour", "undefined", "month", "toString", "padStart", "day", "week", "year", "time", "wordCloudOption", "backgroundColor", "tooltip", "show", "formatter", "params", "series", "gridSize", "sizeRange", "rotation<PERSON>ange", "rotationStep", "shape", "left", "top", "width", "height", "right", "bottom", "drawOutOfBound", "layoutAnimation", "maskImage", "keepAspect", "textStyle", "fontFamily", "fontWeight", "color", "index", "dataIndex", "length", "emphasis", "focus", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "shadowOffsetX", "shadowOffsetY", "categoryPieConfig", "appendPadding", "angleField", "colorField", "radius", "innerRadius", "label", "offset", "content", "fontSize", "fill", "legend", "position", "offsetX", "itemName", "datum", "statistic", "title", "whiteSpace", "overflow", "textOverflow", "reduce", "sum", "interactions", "sourceColumnConfig", "xField", "yField", "opacity", "xAxis", "autoHide", "autoRotate", "meta", "alias", "timelineConfig", "smooth", "point", "stroke", "lineWidth", "line", "area", "fillOpacity", "text", "grid", "lineDash", "yAxis", "min", "nice", "showMarkers", "animation", "appear", "duration", "level", "gutter", "marginBottom", "xs", "sm", "md", "general", "totalCount", "prefix", "valueStyle", "latestCollectTime", "fromNow", "lg", "extra", "option", "opts", "renderer", "notMerge", "lazyUpdate", "span", "href", "bodyStyle", "dataSource", "renderItem", "<PERSON><PERSON>", "Meta", "substring", "collectTime", "trendingCategories", "trendingSources", "overview", "totalScore", "background", "borderRadius", "strong", "positivePercentage", "neutralPercentage", "negativePercentage", "keywordSentiments", "_item$sentiment", "keyword", "sentiment", "emoji", "score", "_c", "$RefreshReg$"], "sources": ["D:/舆情监控/client/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, List, Typography, Tag, Space, Spin, Alert } from 'antd';\nimport {\n  FileTextOutlined,\n  DatabaseOutlined,\n  ClockCircleOutlined,\n  TrophyOutlined,\n  RiseOutlined,\n  PieChartOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  LineChartOutlined\n} from '@ant-design/icons';\nimport { Column, Pie, Line, Area } from '@ant-design/charts';\nimport ReactECharts from 'echarts-for-react';\nimport * as echarts from 'echarts';\nimport 'echarts-wordcloud';\nimport axios from 'axios';\nimport moment from 'moment';\n\nconst { Title, Text } = Typography;\n\nconst Dashboard = () => {\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState(null);\n  const [recentNews, setRecentNews] = useState([]);\n  const [trendingStats, setTrendingStats] = useState(null);\n  const [timelineStats, setTimelineStats] = useState([]);\n  const [sentimentStats, setSentimentStats] = useState(null);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const [statsRes, newsRes, trendingRes, timelineRes, sentimentRes] = await Promise.all([\n        axios.get('/api/stats'),\n        axios.get('/api/news?limit=10'),\n        axios.get('/api/stats/trending?period=day&limit=5'),\n        axios.get('/api/stats/timeline?period=day&days=7'),\n        axios.get('/api/stats/sentiment?period=day&days=7&limit=50')\n      ]);\n\n      setStats(statsRes.data.data);\n      setRecentNews(newsRes.data.data.items);\n      setTrendingStats(trendingRes.data.data);\n      setTimelineStats(timelineRes.data.data.timeline);\n      setSentimentStats(sentimentRes.data.data);\n    } catch (error) {\n      console.error('获取仪表板数据失败:', error);\n      setError('获取数据失败，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>加载中...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Alert\n        message=\"错误\"\n        description={error}\n        type=\"error\"\n        showIcon\n        action={\n          <button onClick={fetchDashboardData}>重试</button>\n        }\n      />\n    );\n  }\n\n  const getCategoryColor = (category) => {\n    const colors = {\n      '技术社区': 'blue',\n      '问答社区': 'green',\n      '社交媒体': 'orange',\n      '新闻媒体': 'red',\n      '财经媒体': 'purple',\n      '科技媒体': 'cyan',\n      '视频平台': 'magenta',\n      '短视频': 'lime',\n      '投资社区': 'gold'\n    };\n    return colors[category] || 'default';\n  };\n\n  // 准备分类饼图数据\n  const categoryPieData = stats?.byCategory?.map(item => ({\n    type: item._id,\n    value: item.count,\n    name: item._id\n  })) || [];\n\n  // 准备数据源柱状图数据\n  const sourceColumnData = stats?.bySource?.slice(0, 10).map(item => ({\n    source: item._id.sourceName,\n    count: item.count\n  })) || [];\n\n  // 准备词云数据\n  const wordCloudData = trendingStats?.trendingKeywords?.map(item => ({\n    name: item._id,\n    value: item.count\n  })) || [];\n\n  // 调试输出\n  console.log('词云数据:', wordCloudData);\n  console.log('热门统计数据:', trendingStats);\n\n  // 准备时间线图表数据\n  const timelineChartData = timelineStats.map(item => {\n    const { _id } = item;\n    let timeLabel;\n    if (_id.hour !== undefined) {\n      timeLabel = `${_id.month.toString().padStart(2, '0')}-${_id.day.toString().padStart(2, '0')} ${_id.hour}:00`;\n    } else if (_id.day !== undefined) {\n      timeLabel = `${_id.month.toString().padStart(2, '0')}-${_id.day.toString().padStart(2, '0')}`;\n    } else if (_id.week !== undefined) {\n      timeLabel = `第${_id.week}周`;\n    } else {\n      timeLabel = `${_id.year}-${_id.month.toString().padStart(2, '0')}`;\n    }\n    return {\n      time: timeLabel,\n      count: item.count\n    };\n  });\n\n  // 词云配置\n  const wordCloudOption = {\n    backgroundColor: 'transparent',\n    tooltip: {\n      show: true,\n      formatter: function (params) {\n        return `${params.name}: ${params.value} 次`;\n      }\n    },\n    series: [{\n      type: 'wordCloud',\n      // 优化布局参数\n      gridSize: 8,  // 增加网格大小，减少重叠\n      sizeRange: [16, 50],  // 调整字体大小范围，使差异更明显\n      rotationRange: [0, 0],  // 禁用旋转，保持水平排列\n      rotationStep: 0,\n      shape: 'circle',\n      left: 'center',\n      top: 'center',\n      width: '90%',  // 稍微缩小宽度，留出边距\n      height: '90%',  // 稍微缩小高度，留出边距\n      right: null,\n      bottom: null,\n      drawOutOfBound: false,\n      layoutAnimation: true,\n      // 优化布局算法\n      maskImage: null,\n      keepAspect: false,\n      // 增加词间距\n      padding: 5,\n      textStyle: {\n        fontFamily: 'Microsoft YaHei, Arial, sans-serif',\n        fontWeight: 'bold',  // 使用粗体，更清晰\n        color: function (params) {\n          // 根据词频使用不同颜色深度\n          const colors = [\n            '#1890ff', '#52c41a', '#faad14', '#f5222d',\n            '#722ed1', '#13c2c2', '#eb2f96', '#fa541c',\n            '#2f54eb', '#a0d911', '#fadb14', '#ff4d4f'\n          ];\n          const index = params.dataIndex % colors.length;\n          return colors[index];\n        }\n      },\n      emphasis: {\n        focus: 'self',\n        textStyle: {\n          shadowBlur: 8,\n          shadowColor: 'rgba(0, 0, 0, 0.3)',\n          shadowOffsetX: 2,\n          shadowOffsetY: 2\n        }\n      },\n      data: wordCloudData && wordCloudData.length > 0 ? wordCloudData : [\n        { name: '暂无数据', value: 1 }\n      ]\n    }]\n  };\n\n  // 分类饼图配置\n  const categoryPieConfig = {\n    appendPadding: 10,\n    data: categoryPieData,\n    angleField: 'value',\n    colorField: 'type',\n    radius: 0.75,\n    innerRadius: 0.3,\n    label: {\n      type: 'inner',\n      offset: '-30%',\n      content: function(data) {\n        return data.value > 20 ? `${data.type}\\n${data.value}` : '';\n      },\n      style: {\n        fontSize: 11,\n        textAlign: 'center',\n        fill: 'white',\n        fontWeight: 'bold'\n      }\n    },\n    legend: {\n      position: 'right',\n      offsetX: -20,\n      itemName: {\n        style: {\n          fontSize: 12\n        }\n      }\n    },\n    tooltip: {\n      formatter: (datum) => {\n        return { name: datum.type, value: `${datum.value} 条` };\n      }\n    },\n    statistic: {\n      title: {\n        style: {\n          whiteSpace: 'pre-wrap',\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n        },\n        content: '总计',\n      },\n      content: {\n        style: {\n          whiteSpace: 'pre-wrap',\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n        },\n        content: categoryPieData.reduce((sum, item) => sum + item.value, 0).toString(),\n      },\n    },\n    interactions: [\n      {\n        type: 'element-selected',\n      },\n      {\n        type: 'element-active',\n      },\n    ],\n  };\n\n  // 数据源柱状图配置\n  const sourceColumnConfig = {\n    data: sourceColumnData,\n    xField: 'source',\n    yField: 'count',\n    label: {\n      position: 'middle',\n      style: {\n        fill: '#FFFFFF',\n        opacity: 0.6,\n      },\n    },\n    xAxis: {\n      label: {\n        autoHide: true,\n        autoRotate: false,\n      },\n    },\n    meta: {\n      source: {\n        alias: '数据源',\n      },\n      count: {\n        alias: '新闻数量',\n      },\n    },\n  };\n\n  // 时间线图表配置\n  const timelineConfig = {\n    data: timelineChartData,\n    xField: 'time',\n    yField: 'count',\n    smooth: true,\n    padding: [20, 20, 50, 60], // 增加左侧padding确保Y轴显示\n    point: {\n      size: 4,\n      shape: 'circle',\n      style: {\n        fill: 'white',\n        stroke: '#1890ff',\n        lineWidth: 2,\n      },\n    },\n    line: {\n      style: {\n        stroke: '#1890ff',\n        lineWidth: 3,\n      },\n    },\n    area: {\n      style: {\n        fill: 'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff',\n        fillOpacity: 0.3,\n      },\n    },\n    xAxis: {\n      position: 'bottom',\n      label: {\n        autoRotate: true,\n        style: {\n          fontSize: 12,\n        },\n      },\n      title: {\n        text: '时间',\n        position: 'end',\n        style: {\n          fontSize: 14,\n          fontWeight: 'bold',\n        },\n      },\n      grid: {\n        line: {\n          style: {\n            stroke: '#f0f0f0',\n            lineWidth: 1,\n            lineDash: [4, 5],\n          },\n        },\n      },\n    },\n    yAxis: {\n      position: 'left',\n      min: 0,\n      nice: true,\n      label: {\n        style: {\n          fontSize: 12,\n        },\n      },\n      title: {\n        text: '更新数量',\n        position: 'end',\n        style: {\n          fontSize: 14,\n          fontWeight: 'bold',\n        },\n      },\n      grid: {\n        line: {\n          style: {\n            stroke: '#f0f0f0',\n            lineWidth: 1,\n            lineDash: [4, 5],\n          },\n        },\n      },\n    },\n    tooltip: {\n      showMarkers: true,\n      formatter: (datum) => {\n        return { name: '更新数量', value: `${datum.count} 条` };\n      },\n    },\n    animation: {\n      appear: {\n        animation: 'path-in',\n        duration: 1000,\n      },\n    },\n  };\n\n  return (\n    <div>\n      <Title level={2}>仪表板</Title>\n      \n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总新闻数量\"\n              value={stats?.general?.totalCount || 0}\n              prefix={<FileTextOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"数据源数量\"\n              value={stats?.bySource?.length || 0}\n              prefix={<DatabaseOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"分类数量\"\n              value={stats?.byCategory?.length || 0}\n              prefix={<TrophyOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"最新更新\"\n              value={stats?.general?.latestCollectTime ? \n                moment(stats.general.latestCollectTime).fromNow() : '暂无数据'}\n              prefix={<ClockCircleOutlined />}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 图表展示区域 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        {/* 分类分布饼图 */}\n        <Col xs={24} lg={12}>\n          <Card title=\"分类分布\" extra={<PieChartOutlined />}>\n            {categoryPieData.length > 0 ? (\n              <Pie {...categoryPieConfig} height={300} />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin />\n                <div style={{ marginTop: 16 }}>加载中...</div>\n              </div>\n            )}\n          </Card>\n        </Col>\n\n        {/* 热门关键词词云 */}\n        <Col xs={24} lg={12}>\n          <Card title=\"热门关键词词云\" extra={<RiseOutlined />}>\n            {trendingStats && trendingStats.trendingKeywords ? (\n              <ReactECharts\n                option={wordCloudOption}\n                style={{ height: '300px' }}\n                opts={{ renderer: 'canvas' }}\n                notMerge={true}\n                lazyUpdate={true}\n              />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin />\n                <div style={{ marginTop: 16 }}>加载中...</div>\n              </div>\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 时间线趋势图 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col span={24}>\n          <Card title=\"数据更新趋势\" extra={<LineChartOutlined />}>\n            {timelineChartData.length > 0 ? (\n              <Area {...timelineConfig} height={300} />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin />\n                <div style={{ marginTop: 16 }}>加载中...</div>\n              </div>\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 数据源统计柱状图 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col span={24}>\n          <Card title=\"热门数据源统计\" extra={<BarChartOutlined />}>\n            {sourceColumnData.length > 0 ? (\n              <Column {...sourceColumnConfig} height={300} />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '50px' }}>\n                <Spin />\n                <div style={{ marginTop: 16 }}>加载中...</div>\n              </div>\n            )}\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]}>\n        {/* 最新新闻 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title=\"最新新闻\"\n            extra={<a href=\"/news\">查看更多</a>}\n            style={{ height: '400px' }}\n            bodyStyle={{ height: '340px', overflow: 'auto' }}\n          >\n            <List\n              dataSource={recentNews}\n              renderItem={(item) => (\n                <List.Item style={{ padding: '8px 0' }}>\n                  <List.Item.Meta\n                    title={\n                      <a href={`/news/${item._id}`} style={{ fontSize: 14 }}>\n                        {item.title.length > 50 ? `${item.title.substring(0, 50)}...` : item.title}\n                      </a>\n                    }\n                    description={\n                      <Space>\n                        <Tag color={getCategoryColor(item.category)} size=\"small\">\n                          {item.category}\n                        </Tag>\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          {item.sourceName}\n                        </Text>\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          {moment(item.collectTime).fromNow()}\n                        </Text>\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n\n        {/* 热门分类列表 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title=\"热门分类\"\n            extra={<RiseOutlined />}\n            style={{ height: '400px' }}\n            bodyStyle={{ height: '340px', overflow: 'auto' }}\n          >\n            <List\n              dataSource={trendingStats?.trendingCategories || []}\n              renderItem={(item) => (\n                <List.Item style={{ padding: '8px 0' }}>\n                  <List.Item.Meta\n                    title={item._id}\n                    description={`${item.count} 条新闻`}\n                  />\n                  <div>\n                    <Tag color={getCategoryColor(item._id)}>\n                      {item.count}\n                    </Tag>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>\n        {/* 热门数据源 */}\n        <Col xs={24} lg={12}>\n          <Card\n            title=\"热门数据源\"\n            style={{ height: '400px' }}\n            bodyStyle={{ height: '340px', overflow: 'auto' }}\n          >\n            <List\n              dataSource={trendingStats?.trendingSources || []}\n              renderItem={(item) => (\n                <List.Item style={{ padding: '8px 0' }}>\n                  <List.Item.Meta\n                    title={item._id.sourceName}\n                    description={`${item.count} 条新闻`}\n                  />\n                  <div>\n                    <Tag color=\"blue\">{item.count}</Tag>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n\n        {/* 热门关键词（带情感分析） */}\n        <Col xs={24} lg={12}>\n          <Card\n            title=\"热门关键词情感分析\"\n            style={{ height: '400px' }}\n            bodyStyle={{ height: '340px', overflow: 'auto' }}\n            extra={\n              sentimentStats?.overview && (\n                <Text type=\"secondary\">\n                  总体情感: {sentimentStats.overview.totalScore}/10\n                </Text>\n              )\n            }\n          >\n            {sentimentStats?.overview && (\n              <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>\n                <Text strong>情感概览</Text>\n                <div style={{ marginTop: 8 }}>\n                  <Text>总体情感指数: {sentimentStats.overview.totalScore}/10</Text>\n                  <div style={{ marginTop: 4 }}>\n                    <Tag color=\"green\">正面 {sentimentStats.overview.positivePercentage}%</Tag>\n                    <Tag color=\"default\">中性 {sentimentStats.overview.neutralPercentage}%</Tag>\n                    <Tag color=\"red\">负面 {sentimentStats.overview.negativePercentage}%</Tag>\n                  </div>\n                </div>\n              </div>\n            )}\n            <List\n              dataSource={sentimentStats?.keywordSentiments || trendingStats?.trendingKeywords || []}\n              renderItem={(item) => (\n                <List.Item style={{ padding: '8px 0' }}>\n                  <List.Item.Meta\n                    title={\n                      <Space>\n                        {item.keyword || item._id}\n                        {item.sentiment?.emoji && <span>{item.sentiment.emoji}</span>}\n                      </Space>\n                    }\n                    description={\n                      <Space>\n                        <Text type=\"secondary\">出现 {item.count} 次</Text>\n                        {item.sentiment && (\n                          <Tag color={\n                            item.sentiment.sentiment === 'positive' ? 'green' :\n                            item.sentiment.sentiment === 'negative' ? 'red' : 'default'\n                          }>\n                            {item.sentiment.sentiment === 'positive' ? '正面' :\n                             item.sentiment.sentiment === 'negative' ? '负面' : '中性'}\n                            {item.sentiment.score}分\n                          </Tag>\n                        )}\n                      </Space>\n                    }\n                  />\n                  <div>\n                    <Tag color=\"blue\">{item.count}</Tag>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAEC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAC3F,SACEC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,EACnBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,iBAAiB,QACZ,mBAAmB;AAC1B,SAASC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,QAAQ,oBAAoB;AAC5D,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAO,KAAKC,OAAO,MAAM,SAAS;AAClC,OAAO,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGxB,UAAU;AAElC,MAAMyB,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,kBAAA,EAAAC,eAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdsD,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChBY,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM,CAACE,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpFpC,KAAK,CAACqC,GAAG,CAAC,YAAY,CAAC,EACvBrC,KAAK,CAACqC,GAAG,CAAC,oBAAoB,CAAC,EAC/BrC,KAAK,CAACqC,GAAG,CAAC,wCAAwC,CAAC,EACnDrC,KAAK,CAACqC,GAAG,CAAC,uCAAuC,CAAC,EAClDrC,KAAK,CAACqC,GAAG,CAAC,iDAAiD,CAAC,CAC7D,CAAC;MAEFnB,QAAQ,CAACY,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;MAC5BlB,aAAa,CAACW,OAAO,CAACO,IAAI,CAACA,IAAI,CAACC,KAAK,CAAC;MACtCjB,gBAAgB,CAACU,WAAW,CAACM,IAAI,CAACA,IAAI,CAAC;MACvCd,gBAAgB,CAACS,WAAW,CAACK,IAAI,CAACA,IAAI,CAACE,QAAQ,CAAC;MAChDd,iBAAiB,CAACQ,YAAY,CAACI,IAAI,CAACA,IAAI,CAAC;IAC3C,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCC,QAAQ,CAAC,cAAc,CAAC;IAC1B,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEZ,OAAA;MAAKuC,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACnD1C,OAAA,CAACnB,IAAI;QAAC8D,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB/C,OAAA;QAAKuC,KAAK,EAAE;UAAES,SAAS,EAAE;QAAG,CAAE;QAAAN,QAAA,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAEV;EAEA,IAAIvB,KAAK,EAAE;IACT,oBACExB,OAAA,CAAClB,KAAK;MACJmE,OAAO,EAAC,cAAI;MACZC,WAAW,EAAE1B,KAAM;MACnB2B,IAAI,EAAC,OAAO;MACZC,QAAQ;MACRC,MAAM,eACJrD,OAAA;QAAQsD,OAAO,EAAE5B,kBAAmB;QAAAgB,QAAA,EAAC;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAChD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEN;EAEA,MAAMQ,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,MAAM,EAAE,OAAO;MACf,MAAM,EAAE,QAAQ;MAChB,MAAM,EAAE,KAAK;MACb,MAAM,EAAE,QAAQ;MAChB,MAAM,EAAE,MAAM;MACd,MAAM,EAAE,SAAS;MACjB,KAAK,EAAE,MAAM;MACb,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,MAAM,CAACD,QAAQ,CAAC,IAAI,SAAS;EACtC,CAAC;;EAED;EACA,MAAME,eAAe,GAAG,CAAA5C,KAAK,aAALA,KAAK,wBAAAT,iBAAA,GAALS,KAAK,CAAE6C,UAAU,cAAAtD,iBAAA,uBAAjBA,iBAAA,CAAmBuD,GAAG,CAACC,IAAI,KAAK;IACtDV,IAAI,EAAEU,IAAI,CAACC,GAAG;IACdC,KAAK,EAAEF,IAAI,CAACG,KAAK;IACjBC,IAAI,EAAEJ,IAAI,CAACC;EACb,CAAC,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMI,gBAAgB,GAAG,CAAApD,KAAK,aAALA,KAAK,wBAAAR,eAAA,GAALQ,KAAK,CAAEqD,QAAQ,cAAA7D,eAAA,uBAAfA,eAAA,CAAiB8D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACR,GAAG,CAACC,IAAI,KAAK;IAClEQ,MAAM,EAAER,IAAI,CAACC,GAAG,CAACQ,UAAU;IAC3BN,KAAK,EAAEH,IAAI,CAACG;EACd,CAAC,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMO,aAAa,GAAG,CAAArD,aAAa,aAAbA,aAAa,wBAAAX,qBAAA,GAAbW,aAAa,CAAEsD,gBAAgB,cAAAjE,qBAAA,uBAA/BA,qBAAA,CAAiCqD,GAAG,CAACC,IAAI,KAAK;IAClEI,IAAI,EAAEJ,IAAI,CAACC,GAAG;IACdC,KAAK,EAAEF,IAAI,CAACG;EACd,CAAC,CAAC,CAAC,KAAI,EAAE;;EAET;EACA1B,OAAO,CAACmC,GAAG,CAAC,OAAO,EAAEF,aAAa,CAAC;EACnCjC,OAAO,CAACmC,GAAG,CAAC,SAAS,EAAEvD,aAAa,CAAC;;EAErC;EACA,MAAMwD,iBAAiB,GAAGtD,aAAa,CAACwC,GAAG,CAACC,IAAI,IAAI;IAClD,MAAM;MAAEC;IAAI,CAAC,GAAGD,IAAI;IACpB,IAAIc,SAAS;IACb,IAAIb,GAAG,CAACc,IAAI,KAAKC,SAAS,EAAE;MAC1BF,SAAS,GAAG,GAAGb,GAAG,CAACgB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIlB,GAAG,CAACmB,GAAG,CAACF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIlB,GAAG,CAACc,IAAI,KAAK;IAC9G,CAAC,MAAM,IAAId,GAAG,CAACmB,GAAG,KAAKJ,SAAS,EAAE;MAChCF,SAAS,GAAG,GAAGb,GAAG,CAACgB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIlB,GAAG,CAACmB,GAAG,CAACF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC/F,CAAC,MAAM,IAAIlB,GAAG,CAACoB,IAAI,KAAKL,SAAS,EAAE;MACjCF,SAAS,GAAG,IAAIb,GAAG,CAACoB,IAAI,GAAG;IAC7B,CAAC,MAAM;MACLP,SAAS,GAAG,GAAGb,GAAG,CAACqB,IAAI,IAAIrB,GAAG,CAACgB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IACpE;IACA,OAAO;MACLI,IAAI,EAAET,SAAS;MACfX,KAAK,EAAEH,IAAI,CAACG;IACd,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,MAAMqB,eAAe,GAAG;IACtBC,eAAe,EAAE,aAAa;IAC9BC,OAAO,EAAE;MACPC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAE;QAC3B,OAAO,GAAGA,MAAM,CAACzB,IAAI,KAAKyB,MAAM,CAAC3B,KAAK,IAAI;MAC5C;IACF,CAAC;IACD4B,MAAM,EAAE,CAAC;MACPxC,IAAI,EAAE,WAAW;MACjB;MACAyC,QAAQ,EAAE,CAAC;MAAG;MACdC,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;MAAG;MACtBC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAAG;MACxBC,YAAY,EAAE,CAAC;MACfC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,QAAQ;MACdC,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE,KAAK;MAAG;MACfC,MAAM,EAAE,KAAK;MAAG;MAChBC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE,IAAI;MACrB;MACAC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,KAAK;MACjB;MACAjE,OAAO,EAAE,CAAC;MACVkE,SAAS,EAAE;QACTC,UAAU,EAAE,oCAAoC;QAChDC,UAAU,EAAE,MAAM;QAAG;QACrBC,KAAK,EAAE,SAAAA,CAAUpB,MAAM,EAAE;UACvB;UACA,MAAMjC,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAC1C,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;UACD,MAAMsD,KAAK,GAAGrB,MAAM,CAACsB,SAAS,GAAGvD,MAAM,CAACwD,MAAM;UAC9C,OAAOxD,MAAM,CAACsD,KAAK,CAAC;QACtB;MACF,CAAC;MACDG,QAAQ,EAAE;QACRC,KAAK,EAAE,MAAM;QACbR,SAAS,EAAE;UACTS,UAAU,EAAE,CAAC;UACbC,WAAW,EAAE,oBAAoB;UACjCC,aAAa,EAAE,CAAC;UAChBC,aAAa,EAAE;QACjB;MACF,CAAC;MACDpF,IAAI,EAAEoC,aAAa,IAAIA,aAAa,CAAC0C,MAAM,GAAG,CAAC,GAAG1C,aAAa,GAAG,CAChE;QAAEN,IAAI,EAAE,MAAM;QAAEF,KAAK,EAAE;MAAE,CAAC;IAE9B,CAAC;EACH,CAAC;;EAED;EACA,MAAMyD,iBAAiB,GAAG;IACxBC,aAAa,EAAE,EAAE;IACjBtF,IAAI,EAAEuB,eAAe;IACrBgE,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,GAAG;IAChBC,KAAK,EAAE;MACL3E,IAAI,EAAE,OAAO;MACb4E,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,SAAAA,CAAS7F,IAAI,EAAE;QACtB,OAAOA,IAAI,CAAC4B,KAAK,GAAG,EAAE,GAAG,GAAG5B,IAAI,CAACgB,IAAI,KAAKhB,IAAI,CAAC4B,KAAK,EAAE,GAAG,EAAE;MAC7D,CAAC;MACDxB,KAAK,EAAE;QACL0F,QAAQ,EAAE,EAAE;QACZzF,SAAS,EAAE,QAAQ;QACnB0F,IAAI,EAAE,OAAO;QACbrB,UAAU,EAAE;MACd;IACF,CAAC;IACDsB,MAAM,EAAE;MACNC,QAAQ,EAAE,OAAO;MACjBC,OAAO,EAAE,CAAC,EAAE;MACZC,QAAQ,EAAE;QACR/F,KAAK,EAAE;UACL0F,QAAQ,EAAE;QACZ;MACF;IACF,CAAC;IACD1C,OAAO,EAAE;MACPE,SAAS,EAAG8C,KAAK,IAAK;QACpB,OAAO;UAAEtE,IAAI,EAAEsE,KAAK,CAACpF,IAAI;UAAEY,KAAK,EAAE,GAAGwE,KAAK,CAACxE,KAAK;QAAK,CAAC;MACxD;IACF,CAAC;IACDyE,SAAS,EAAE;MACTC,KAAK,EAAE;QACLlG,KAAK,EAAE;UACLmG,UAAU,EAAE,UAAU;UACtBC,QAAQ,EAAE,QAAQ;UAClBC,YAAY,EAAE;QAChB,CAAC;QACDZ,OAAO,EAAE;MACX,CAAC;MACDA,OAAO,EAAE;QACPzF,KAAK,EAAE;UACLmG,UAAU,EAAE,UAAU;UACtBC,QAAQ,EAAE,QAAQ;UAClBC,YAAY,EAAE;QAChB,CAAC;QACDZ,OAAO,EAAEtE,eAAe,CAACmF,MAAM,CAAC,CAACC,GAAG,EAAEjF,IAAI,KAAKiF,GAAG,GAAGjF,IAAI,CAACE,KAAK,EAAE,CAAC,CAAC,CAACgB,QAAQ,CAAC;MAC/E;IACF,CAAC;IACDgE,YAAY,EAAE,CACZ;MACE5F,IAAI,EAAE;IACR,CAAC,EACD;MACEA,IAAI,EAAE;IACR,CAAC;EAEL,CAAC;;EAED;EACA,MAAM6F,kBAAkB,GAAG;IACzB7G,IAAI,EAAE+B,gBAAgB;IACtB+E,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,OAAO;IACfpB,KAAK,EAAE;MACLM,QAAQ,EAAE,QAAQ;MAClB7F,KAAK,EAAE;QACL2F,IAAI,EAAE,SAAS;QACfiB,OAAO,EAAE;MACX;IACF,CAAC;IACDC,KAAK,EAAE;MACLtB,KAAK,EAAE;QACLuB,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE;MACd;IACF,CAAC;IACDC,IAAI,EAAE;MACJlF,MAAM,EAAE;QACNmF,KAAK,EAAE;MACT,CAAC;MACDxF,KAAK,EAAE;QACLwF,KAAK,EAAE;MACT;IACF;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG;IACrBtH,IAAI,EAAEuC,iBAAiB;IACvBuE,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,OAAO;IACfQ,MAAM,EAAE,IAAI;IACZjH,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAAE;IAC3BkH,KAAK,EAAE;MACLhH,IAAI,EAAE,CAAC;MACPqD,KAAK,EAAE,QAAQ;MACfzD,KAAK,EAAE;QACL2F,IAAI,EAAE,OAAO;QACb0B,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE;MACb;IACF,CAAC;IACDC,IAAI,EAAE;MACJvH,KAAK,EAAE;QACLqH,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE;MACb;IACF,CAAC;IACDE,IAAI,EAAE;MACJxH,KAAK,EAAE;QACL2F,IAAI,EAAE,wCAAwC;QAC9C8B,WAAW,EAAE;MACf;IACF,CAAC;IACDZ,KAAK,EAAE;MACLhB,QAAQ,EAAE,QAAQ;MAClBN,KAAK,EAAE;QACLwB,UAAU,EAAE,IAAI;QAChB/G,KAAK,EAAE;UACL0F,QAAQ,EAAE;QACZ;MACF,CAAC;MACDQ,KAAK,EAAE;QACLwB,IAAI,EAAE,IAAI;QACV7B,QAAQ,EAAE,KAAK;QACf7F,KAAK,EAAE;UACL0F,QAAQ,EAAE,EAAE;UACZpB,UAAU,EAAE;QACd;MACF,CAAC;MACDqD,IAAI,EAAE;QACJJ,IAAI,EAAE;UACJvH,KAAK,EAAE;YACLqH,MAAM,EAAE,SAAS;YACjBC,SAAS,EAAE,CAAC;YACZM,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC;UACjB;QACF;MACF;IACF,CAAC;IACDC,KAAK,EAAE;MACLhC,QAAQ,EAAE,MAAM;MAChBiC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,IAAI;MACVxC,KAAK,EAAE;QACLvF,KAAK,EAAE;UACL0F,QAAQ,EAAE;QACZ;MACF,CAAC;MACDQ,KAAK,EAAE;QACLwB,IAAI,EAAE,MAAM;QACZ7B,QAAQ,EAAE,KAAK;QACf7F,KAAK,EAAE;UACL0F,QAAQ,EAAE,EAAE;UACZpB,UAAU,EAAE;QACd;MACF,CAAC;MACDqD,IAAI,EAAE;QACJJ,IAAI,EAAE;UACJvH,KAAK,EAAE;YACLqH,MAAM,EAAE,SAAS;YACjBC,SAAS,EAAE,CAAC;YACZM,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC;UACjB;QACF;MACF;IACF,CAAC;IACD5E,OAAO,EAAE;MACPgF,WAAW,EAAE,IAAI;MACjB9E,SAAS,EAAG8C,KAAK,IAAK;QACpB,OAAO;UAAEtE,IAAI,EAAE,MAAM;UAAEF,KAAK,EAAE,GAAGwE,KAAK,CAACvE,KAAK;QAAK,CAAC;MACpD;IACF,CAAC;IACDwG,SAAS,EAAE;MACTC,MAAM,EAAE;QACND,SAAS,EAAE,SAAS;QACpBE,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EAED,oBACE1K,OAAA;IAAA0C,QAAA,gBACE1C,OAAA,CAACC,KAAK;MAAC0K,KAAK,EAAE,CAAE;MAAAjI,QAAA,EAAC;IAAG;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG5B/C,OAAA,CAAC3B,GAAG;MAACuM,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrI,KAAK,EAAE;QAAEsI,YAAY,EAAE;MAAG,CAAE;MAAAnI,QAAA,gBACjD1C,OAAA,CAAC1B,GAAG;QAACwM,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtI,QAAA,eACzB1C,OAAA,CAACzB,IAAI;UAAAmE,QAAA,eACH1C,OAAA,CAACxB,SAAS;YACRiK,KAAK,EAAC,gCAAO;YACb1E,KAAK,EAAE,CAAAjD,KAAK,aAALA,KAAK,wBAAAN,cAAA,GAALM,KAAK,CAAEmK,OAAO,cAAAzK,cAAA,uBAAdA,cAAA,CAAgB0K,UAAU,KAAI,CAAE;YACvCC,MAAM,eAAEnL,OAAA,CAACjB,gBAAgB;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BqI,UAAU,EAAE;cAAEtE,KAAK,EAAE;YAAU;UAAE;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/C,OAAA,CAAC1B,GAAG;QAACwM,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtI,QAAA,eACzB1C,OAAA,CAACzB,IAAI;UAAAmE,QAAA,eACH1C,OAAA,CAACxB,SAAS;YACRiK,KAAK,EAAC,gCAAO;YACb1E,KAAK,EAAE,CAAAjD,KAAK,aAALA,KAAK,wBAAAL,gBAAA,GAALK,KAAK,CAAEqD,QAAQ,cAAA1D,gBAAA,uBAAfA,gBAAA,CAAiBwG,MAAM,KAAI,CAAE;YACpCkE,MAAM,eAAEnL,OAAA,CAAChB,gBAAgB;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BqI,UAAU,EAAE;cAAEtE,KAAK,EAAE;YAAU;UAAE;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/C,OAAA,CAAC1B,GAAG;QAACwM,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtI,QAAA,eACzB1C,OAAA,CAACzB,IAAI;UAAAmE,QAAA,eACH1C,OAAA,CAACxB,SAAS;YACRiK,KAAK,EAAC,0BAAM;YACZ1E,KAAK,EAAE,CAAAjD,KAAK,aAALA,KAAK,wBAAAJ,kBAAA,GAALI,KAAK,CAAE6C,UAAU,cAAAjD,kBAAA,uBAAjBA,kBAAA,CAAmBuG,MAAM,KAAI,CAAE;YACtCkE,MAAM,eAAEnL,OAAA,CAACd,cAAc;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BqI,UAAU,EAAE;cAAEtE,KAAK,EAAE;YAAU;UAAE;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/C,OAAA,CAAC1B,GAAG;QAACwM,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtI,QAAA,eACzB1C,OAAA,CAACzB,IAAI;UAAAmE,QAAA,eACH1C,OAAA,CAACxB,SAAS;YACRiK,KAAK,EAAC,0BAAM;YACZ1E,KAAK,EAAEjD,KAAK,aAALA,KAAK,gBAAAH,eAAA,GAALG,KAAK,CAAEmK,OAAO,cAAAtK,eAAA,eAAdA,eAAA,CAAgB0K,iBAAiB,GACtCvL,MAAM,CAACgB,KAAK,CAACmK,OAAO,CAACI,iBAAiB,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,MAAO;YAC7DH,MAAM,eAAEnL,OAAA,CAACf,mBAAmB;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCqI,UAAU,EAAE;cAAEtE,KAAK,EAAE;YAAU;UAAE;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA,CAAC3B,GAAG;MAACuM,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrI,KAAK,EAAE;QAAEsI,YAAY,EAAE;MAAG,CAAE;MAAAnI,QAAA,gBAEjD1C,OAAA,CAAC1B,GAAG;QAACwM,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAA7I,QAAA,eAClB1C,OAAA,CAACzB,IAAI;UAACkK,KAAK,EAAC,0BAAM;UAAC+C,KAAK,eAAExL,OAAA,CAACZ,gBAAgB;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAL,QAAA,EAC5CgB,eAAe,CAACuD,MAAM,GAAG,CAAC,gBACzBjH,OAAA,CAACR,GAAG;YAAA,GAAKgI,iBAAiB;YAAEpB,MAAM,EAAE;UAAI;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE3C/C,OAAA;YAAKuC,KAAK,EAAE;cAAEC,SAAS,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACnD1C,OAAA,CAACnB,IAAI;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACR/C,OAAA;cAAKuC,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAG,CAAE;cAAAN,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN/C,OAAA,CAAC1B,GAAG;QAACwM,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAA7I,QAAA,eAClB1C,OAAA,CAACzB,IAAI;UAACkK,KAAK,EAAC,4CAAS;UAAC+C,KAAK,eAAExL,OAAA,CAACb,YAAY;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAL,QAAA,EAC3CxB,aAAa,IAAIA,aAAa,CAACsD,gBAAgB,gBAC9CxE,OAAA,CAACL,YAAY;YACX8L,MAAM,EAAEpG,eAAgB;YACxB9C,KAAK,EAAE;cAAE6D,MAAM,EAAE;YAAQ,CAAE;YAC3BsF,IAAI,EAAE;cAAEC,QAAQ,EAAE;YAAS,CAAE;YAC7BC,QAAQ,EAAE,IAAK;YACfC,UAAU,EAAE;UAAK;YAAAjJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,gBAEF/C,OAAA;YAAKuC,KAAK,EAAE;cAAEC,SAAS,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACnD1C,OAAA,CAACnB,IAAI;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACR/C,OAAA;cAAKuC,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAG,CAAE;cAAAN,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA,CAAC3B,GAAG;MAACuM,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrI,KAAK,EAAE;QAAEsI,YAAY,EAAE;MAAG,CAAE;MAAAnI,QAAA,eACjD1C,OAAA,CAAC1B,GAAG;QAACwN,IAAI,EAAE,EAAG;QAAApJ,QAAA,eACZ1C,OAAA,CAACzB,IAAI;UAACkK,KAAK,EAAC,sCAAQ;UAAC+C,KAAK,eAAExL,OAAA,CAACV,iBAAiB;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAL,QAAA,EAC/CgC,iBAAiB,CAACuC,MAAM,GAAG,CAAC,gBAC3BjH,OAAA,CAACN,IAAI;YAAA,GAAK+J,cAAc;YAAErD,MAAM,EAAE;UAAI;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEzC/C,OAAA;YAAKuC,KAAK,EAAE;cAAEC,SAAS,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACnD1C,OAAA,CAACnB,IAAI;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACR/C,OAAA;cAAKuC,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAG,CAAE;cAAAN,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA,CAAC3B,GAAG;MAACuM,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrI,KAAK,EAAE;QAAEsI,YAAY,EAAE;MAAG,CAAE;MAAAnI,QAAA,eACjD1C,OAAA,CAAC1B,GAAG;QAACwN,IAAI,EAAE,EAAG;QAAApJ,QAAA,eACZ1C,OAAA,CAACzB,IAAI;UAACkK,KAAK,EAAC,4CAAS;UAAC+C,KAAK,eAAExL,OAAA,CAACX,gBAAgB;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAL,QAAA,EAC/CwB,gBAAgB,CAAC+C,MAAM,GAAG,CAAC,gBAC1BjH,OAAA,CAACT,MAAM;YAAA,GAAKyJ,kBAAkB;YAAE5C,MAAM,EAAE;UAAI;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE/C/C,OAAA;YAAKuC,KAAK,EAAE;cAAEC,SAAS,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACnD1C,OAAA,CAACnB,IAAI;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACR/C,OAAA;cAAKuC,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAG,CAAE;cAAAN,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/C,OAAA,CAAC3B,GAAG;MAACuM,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAlI,QAAA,gBAEpB1C,OAAA,CAAC1B,GAAG;QAACwM,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAA7I,QAAA,eAClB1C,OAAA,CAACzB,IAAI;UACHkK,KAAK,EAAC,0BAAM;UACZ+C,KAAK,eAAExL,OAAA;YAAG+L,IAAI,EAAC,OAAO;YAAArJ,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAE;UAChCR,KAAK,EAAE;YAAE6D,MAAM,EAAE;UAAQ,CAAE;UAC3B4F,SAAS,EAAE;YAAE5F,MAAM,EAAE,OAAO;YAAEuC,QAAQ,EAAE;UAAO,CAAE;UAAAjG,QAAA,eAEjD1C,OAAA,CAACvB,IAAI;YACHwN,UAAU,EAAEjL,UAAW;YACvBkL,UAAU,EAAGrI,IAAI,iBACf7D,OAAA,CAACvB,IAAI,CAAC0N,IAAI;cAAC5J,KAAK,EAAE;gBAAEE,OAAO,EAAE;cAAQ,CAAE;cAAAC,QAAA,eACrC1C,OAAA,CAACvB,IAAI,CAAC0N,IAAI,CAACC,IAAI;gBACb3D,KAAK,eACHzI,OAAA;kBAAG+L,IAAI,EAAE,SAASlI,IAAI,CAACC,GAAG,EAAG;kBAACvB,KAAK,EAAE;oBAAE0F,QAAQ,EAAE;kBAAG,CAAE;kBAAAvF,QAAA,EACnDmB,IAAI,CAAC4E,KAAK,CAACxB,MAAM,GAAG,EAAE,GAAG,GAAGpD,IAAI,CAAC4E,KAAK,CAAC4D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAGxI,IAAI,CAAC4E;gBAAK;kBAAA7F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CACJ;gBACDG,WAAW,eACTlD,OAAA,CAACpB,KAAK;kBAAA8D,QAAA,gBACJ1C,OAAA,CAACrB,GAAG;oBAACmI,KAAK,EAAEvD,gBAAgB,CAACM,IAAI,CAACL,QAAQ,CAAE;oBAACb,IAAI,EAAC,OAAO;oBAAAD,QAAA,EACtDmB,IAAI,CAACL;kBAAQ;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACN/C,OAAA,CAACE,IAAI;oBAACiD,IAAI,EAAC,WAAW;oBAACZ,KAAK,EAAE;sBAAE0F,QAAQ,EAAE;oBAAG,CAAE;oBAAAvF,QAAA,EAC5CmB,IAAI,CAACS;kBAAU;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACP/C,OAAA,CAACE,IAAI;oBAACiD,IAAI,EAAC,WAAW;oBAACZ,KAAK,EAAE;sBAAE0F,QAAQ,EAAE;oBAAG,CAAE;oBAAAvF,QAAA,EAC5C5C,MAAM,CAAC+D,IAAI,CAACyI,WAAW,CAAC,CAAChB,OAAO,CAAC;kBAAC;oBAAA1I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN/C,OAAA,CAAC1B,GAAG;QAACwM,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAA7I,QAAA,eAClB1C,OAAA,CAACzB,IAAI;UACHkK,KAAK,EAAC,0BAAM;UACZ+C,KAAK,eAAExL,OAAA,CAACb,YAAY;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBR,KAAK,EAAE;YAAE6D,MAAM,EAAE;UAAQ,CAAE;UAC3B4F,SAAS,EAAE;YAAE5F,MAAM,EAAE,OAAO;YAAEuC,QAAQ,EAAE;UAAO,CAAE;UAAAjG,QAAA,eAEjD1C,OAAA,CAACvB,IAAI;YACHwN,UAAU,EAAE,CAAA/K,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqL,kBAAkB,KAAI,EAAG;YACpDL,UAAU,EAAGrI,IAAI,iBACf7D,OAAA,CAACvB,IAAI,CAAC0N,IAAI;cAAC5J,KAAK,EAAE;gBAAEE,OAAO,EAAE;cAAQ,CAAE;cAAAC,QAAA,gBACrC1C,OAAA,CAACvB,IAAI,CAAC0N,IAAI,CAACC,IAAI;gBACb3D,KAAK,EAAE5E,IAAI,CAACC,GAAI;gBAChBZ,WAAW,EAAE,GAAGW,IAAI,CAACG,KAAK;cAAO;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACF/C,OAAA;gBAAA0C,QAAA,eACE1C,OAAA,CAACrB,GAAG;kBAACmI,KAAK,EAAEvD,gBAAgB,CAACM,IAAI,CAACC,GAAG,CAAE;kBAAApB,QAAA,EACpCmB,IAAI,CAACG;gBAAK;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/C,OAAA,CAAC3B,GAAG;MAACuM,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrI,KAAK,EAAE;QAAES,SAAS,EAAE;MAAG,CAAE;MAAAN,QAAA,gBAE9C1C,OAAA,CAAC1B,GAAG;QAACwM,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAA7I,QAAA,eAClB1C,OAAA,CAACzB,IAAI;UACHkK,KAAK,EAAC,gCAAO;UACblG,KAAK,EAAE;YAAE6D,MAAM,EAAE;UAAQ,CAAE;UAC3B4F,SAAS,EAAE;YAAE5F,MAAM,EAAE,OAAO;YAAEuC,QAAQ,EAAE;UAAO,CAAE;UAAAjG,QAAA,eAEjD1C,OAAA,CAACvB,IAAI;YACHwN,UAAU,EAAE,CAAA/K,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsL,eAAe,KAAI,EAAG;YACjDN,UAAU,EAAGrI,IAAI,iBACf7D,OAAA,CAACvB,IAAI,CAAC0N,IAAI;cAAC5J,KAAK,EAAE;gBAAEE,OAAO,EAAE;cAAQ,CAAE;cAAAC,QAAA,gBACrC1C,OAAA,CAACvB,IAAI,CAAC0N,IAAI,CAACC,IAAI;gBACb3D,KAAK,EAAE5E,IAAI,CAACC,GAAG,CAACQ,UAAW;gBAC3BpB,WAAW,EAAE,GAAGW,IAAI,CAACG,KAAK;cAAO;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACF/C,OAAA;gBAAA0C,QAAA,eACE1C,OAAA,CAACrB,GAAG;kBAACmI,KAAK,EAAC,MAAM;kBAAApE,QAAA,EAAEmB,IAAI,CAACG;gBAAK;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN/C,OAAA,CAAC1B,GAAG;QAACwM,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAA7I,QAAA,eAClB1C,OAAA,CAACzB,IAAI;UACHkK,KAAK,EAAC,wDAAW;UACjBlG,KAAK,EAAE;YAAE6D,MAAM,EAAE;UAAQ,CAAE;UAC3B4F,SAAS,EAAE;YAAE5F,MAAM,EAAE,OAAO;YAAEuC,QAAQ,EAAE;UAAO,CAAE;UACjD6C,KAAK,EACH,CAAAlK,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEmL,QAAQ,kBACtBzM,OAAA,CAACE,IAAI;YAACiD,IAAI,EAAC,WAAW;YAAAT,QAAA,GAAC,4BACf,EAACpB,cAAc,CAACmL,QAAQ,CAACC,UAAU,EAAC,KAC5C;UAAA;YAAA9J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAET;UAAAL,QAAA,GAEA,CAAApB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEmL,QAAQ,kBACvBzM,OAAA;YAAKuC,KAAK,EAAE;cAAEsI,YAAY,EAAE,EAAE;cAAEpI,OAAO,EAAE,EAAE;cAAEkK,UAAU,EAAE,SAAS;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAAlK,QAAA,gBACpF1C,OAAA,CAACE,IAAI;cAAC2M,MAAM;cAAAnK,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxB/C,OAAA;cAAKuC,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAE,CAAE;cAAAN,QAAA,gBAC3B1C,OAAA,CAACE,IAAI;gBAAAwC,QAAA,GAAC,wCAAQ,EAACpB,cAAc,CAACmL,QAAQ,CAACC,UAAU,EAAC,KAAG;cAAA;gBAAA9J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5D/C,OAAA;gBAAKuC,KAAK,EAAE;kBAAES,SAAS,EAAE;gBAAE,CAAE;gBAAAN,QAAA,gBAC3B1C,OAAA,CAACrB,GAAG;kBAACmI,KAAK,EAAC,OAAO;kBAAApE,QAAA,GAAC,eAAG,EAACpB,cAAc,CAACmL,QAAQ,CAACK,kBAAkB,EAAC,GAAC;gBAAA;kBAAAlK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzE/C,OAAA,CAACrB,GAAG;kBAACmI,KAAK,EAAC,SAAS;kBAAApE,QAAA,GAAC,eAAG,EAACpB,cAAc,CAACmL,QAAQ,CAACM,iBAAiB,EAAC,GAAC;gBAAA;kBAAAnK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1E/C,OAAA,CAACrB,GAAG;kBAACmI,KAAK,EAAC,KAAK;kBAAApE,QAAA,GAAC,eAAG,EAACpB,cAAc,CAACmL,QAAQ,CAACO,kBAAkB,EAAC,GAAC;gBAAA;kBAAApK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eACD/C,OAAA,CAACvB,IAAI;YACHwN,UAAU,EAAE,CAAA3K,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2L,iBAAiB,MAAI/L,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsD,gBAAgB,KAAI,EAAG;YACvF0H,UAAU,EAAGrI,IAAI;cAAA,IAAAqJ,eAAA;cAAA,oBACflN,OAAA,CAACvB,IAAI,CAAC0N,IAAI;gBAAC5J,KAAK,EAAE;kBAAEE,OAAO,EAAE;gBAAQ,CAAE;gBAAAC,QAAA,gBACrC1C,OAAA,CAACvB,IAAI,CAAC0N,IAAI,CAACC,IAAI;kBACb3D,KAAK,eACHzI,OAAA,CAACpB,KAAK;oBAAA8D,QAAA,GACHmB,IAAI,CAACsJ,OAAO,IAAItJ,IAAI,CAACC,GAAG,EACxB,EAAAoJ,eAAA,GAAArJ,IAAI,CAACuJ,SAAS,cAAAF,eAAA,uBAAdA,eAAA,CAAgBG,KAAK,kBAAIrN,OAAA;sBAAA0C,QAAA,EAAOmB,IAAI,CAACuJ,SAAS,CAACC;oBAAK;sBAAAzK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CACR;kBACDG,WAAW,eACTlD,OAAA,CAACpB,KAAK;oBAAA8D,QAAA,gBACJ1C,OAAA,CAACE,IAAI;sBAACiD,IAAI,EAAC,WAAW;sBAAAT,QAAA,GAAC,eAAG,EAACmB,IAAI,CAACG,KAAK,EAAC,SAAE;oBAAA;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC9Cc,IAAI,CAACuJ,SAAS,iBACbpN,OAAA,CAACrB,GAAG;sBAACmI,KAAK,EACRjD,IAAI,CAACuJ,SAAS,CAACA,SAAS,KAAK,UAAU,GAAG,OAAO,GACjDvJ,IAAI,CAACuJ,SAAS,CAACA,SAAS,KAAK,UAAU,GAAG,KAAK,GAAG,SACnD;sBAAA1K,QAAA,GACEmB,IAAI,CAACuJ,SAAS,CAACA,SAAS,KAAK,UAAU,GAAG,IAAI,GAC9CvJ,IAAI,CAACuJ,SAAS,CAACA,SAAS,KAAK,UAAU,GAAG,IAAI,GAAG,IAAI,EACrDvJ,IAAI,CAACuJ,SAAS,CAACE,KAAK,EAAC,QACxB;oBAAA;sBAAA1K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBACR;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACF/C,OAAA;kBAAA0C,QAAA,eACE1C,OAAA,CAACrB,GAAG;oBAACmI,KAAK,EAAC,MAAM;oBAAApE,QAAA,EAAEmB,IAAI,CAACG;kBAAK;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;UACZ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CA/nBID,SAAS;AAAAoN,EAAA,GAATpN,SAAS;AAioBf,eAAeA,SAAS;AAAC,IAAAoN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}